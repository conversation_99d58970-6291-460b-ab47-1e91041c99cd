# OpenAI Client 使用指南

## 🎯 概述

我已经成功使用官方的 `github.com/openai/openai-go` SDK 重新实现了 OpenAI 客户端。新的实现提供了更好的性能、更强的类型安全性和更完整的错误处理。

## 📦 依赖

```bash
go get github.com/openai/openai-go
```

## 🚀 快速开始

### 基本使用

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    "rpa_server/pkg/llm"
)

func main() {
    // 创建 OpenAI 客户端
    client := llm.NewOpenAIClientV2("your-api-key")
    client.SetModel("gpt-4").SetTimeout(30 * time.Second)
    
    // 基本对话
    messages := []llm.Message{
        {Role: "system", Content: "You are a helpful RPA assistant."},
        {Role: "user", Content: "How do I automate a login process?"},
    }
    
    ctx := context.Background()
    response, err := client.Chat(ctx, messages)
    if err != nil {
        log.Fatal(err)
    }
    
    fmt.Println("Response:", response.Content)
    fmt.Printf("Tokens used: %d\n", response.Usage.TotalTokens)
}
```

### 使用工厂模式

```go
// 自动从环境变量配置
client := llm.NewClientFromEnv()

// 手动配置
config := &llm.Config{
    Type:   llm.ClientTypeOpenAI,
    APIKey: "your-api-key",
    Model:  "gpt-4",
}
client := llm.NewClient(config)
```

### 便捷函数

```go
// 快速任务规划
plan, err := llm.QuickPlan("automate invoice processing")

// 快速目标分析
analysis, err := llm.QuickAnalyze("complex data migration")

// 快速对话
response, err := llm.QuickChat("Explain RPA in simple terms")

// 快速代码生成
code, err := llm.QuickCode("create email validation function", "go")
```

## 🔧 环境配置

### 环境变量

```bash
# OpenAI API 密钥
export OPENAI_API_KEY="your-openai-api-key"

# 可选：指定模型
export LLM_MODEL="gpt-4"

# 可选：强制使用 Mock 模式（开发时）
export LLM_MOCK_MODE="true"
```

### 配置选项

```go
client := llm.NewOpenAIClientV2("api-key")

// 设置模型
client.SetModel("gpt-4")

// 设置超时
client.SetTimeout(60 * time.Second)
```

## 📋 功能特性

### 1. 任务规划 (PlanTasks)

```go
goal := "automate customer onboarding process"
context := map[string]any{
    "system": "CRM software",
    "documents": []string{"ID", "proof of address"},
}

plan, err := client.PlanTasks(ctx, goal, context)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Plan: %s\n", plan.Description)
fmt.Printf("Complexity: %s\n", plan.Complexity)

for i, task := range plan.Tasks {
    fmt.Printf("%d. %s (%s)\n", i+1, task.Name, task.Action)
    if len(task.Dependencies) > 0 {
        fmt.Printf("   Dependencies: %v\n", task.Dependencies)
    }
}
```

### 2. 目标分析 (AnalyzeGoal)

```go
goal := "automate financial report generation"

analysis, err := client.AnalyzeGoal(ctx, goal)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Complexity: %s\n", analysis.Complexity)
fmt.Printf("Feasibility: %s\n", analysis.Feasibility)
fmt.Printf("Keywords: %v\n", analysis.Keywords)
fmt.Printf("Required Actions: %v\n", analysis.Actions)

for _, suggestion := range analysis.Suggestions {
    fmt.Printf("- %s\n", suggestion)
}
```

### 3. 代码生成 (GenerateCode)

```go
prompt := "Create a Go function that validates credit card numbers using Luhn algorithm"
language := "go"

code, err := client.GenerateCode(ctx, prompt, language)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Generated %s code:\n", code.Language)
fmt.Printf("```%s\n%s\n```\n", code.Language, code.Code)
fmt.Printf("Explanation: %s\n", code.Explanation)
```

## 🔄 与 Workflow 集成

```go
// 创建 LLM 增强的工作流
llmClient := llm.NewOpenAIClientV2("your-api-key")
planner := workflow.llmPlannerNode(llmClient)
executor := workflow.executorNode()

// 设置 RPA 动作函数
executor.SetParams(map[string]any{
    "action_func": func(ctx *workflow.WorkContext, action string, params map[string]any) (any, error) {
        // 调用您的 RPA 服务
        return yourRPAService.ExecuteAction(action, params)
    },
})

// 连接节点
planner.Next("plan_ready", executor)

// 运行工作流
flow := workflow.NewFlow(planner)
ctx := workflow.WithParam(context.Background(), nil)
ctx.SetValue("goal", "automate user registration")

result, err := flow.Run(ctx)
```

## 🛡️ 错误处理

新的 OpenAI 客户端提供了完善的错误处理：

```go
response, err := client.Chat(ctx, messages)
if err != nil {
    log.Printf("Request failed: %v", err)
    return
}

if response.Error != "" {
    log.Printf("API error: %s", response.Error)
    return
}

// 正常处理响应
fmt.Println(response.Content)
```

## 🧪 测试和开发

### Mock 客户端

开发时可以使用 Mock 客户端：

```go
// 创建 Mock 客户端
mockClient := llm.NewMockLLMClient()
mockClient.SetDelay(100 * time.Millisecond) // 模拟网络延迟

// 使用方式与真实客户端相同
plan, err := mockClient.PlanTasks(ctx, "test goal", nil)
```

### 测试

```bash
# 运行所有 LLM 测试
go test ./pkg/llm -v

# 运行特定测试
go test ./pkg/llm -v -run TestOpenAIClientV2

# 跳过需要 API 密钥的测试
go test ./pkg/llm -v -run "^(TestMockLLMClient|TestLLMFactory)$"
```

## 🔍 高级功能

### JSON 响应解析

客户端自动处理 JSON 解析，包括从 Markdown 代码块中提取 JSON：

```go
// 如果 LLM 返回的不是纯 JSON，客户端会尝试提取
// 例如：LLM 返回 "Here's the plan: ```json\n{...}\n```"
// 客户端会自动提取 JSON 部分
```

### 代码提取

支持从 Markdown 代码块中提取代码：

```go
// 自动从以下格式提取代码：
// ```go
// func example() {}
// ```
```

## 📊 性能优化

1. **连接复用**: 客户端内部使用连接池
2. **超时控制**: 可配置的请求超时
3. **错误重试**: 内置的错误处理和回退机制
4. **内存优化**: 高效的 JSON 解析和内存使用

## 🔮 最佳实践

1. **API 密钥管理**: 使用环境变量存储 API 密钥
2. **错误处理**: 始终检查错误和 API 错误响应
3. **超时设置**: 根据应用需求设置合适的超时时间
4. **模型选择**: 根据任务复杂度选择合适的模型
5. **成本控制**: 监控 token 使用量，实现成本控制

## 🆕 新特性

相比旧版本，新的 OpenAI 客户端提供了：

- ✅ 使用官方 SDK，更好的兼容性
- ✅ 更强的类型安全性
- ✅ 更完善的错误处理
- ✅ 自动 JSON 提取和解析
- ✅ 更好的性能和内存使用
- ✅ 完整的测试覆盖

这个新的实现为您的 RPA 系统提供了更可靠、更高效的 LLM 集成能力！
