package main

import (
	"context"
	"fmt"
	"log"
	"rpa_server/pkg/workflow"
)

func main() {
	fmt.Println("=== RPA Agent Demo ===")

	// Demo 1: Basic agent workflow
	fmt.Println("\n1. Basic Agent Workflow Demo")
	runBasicAgentDemo()

	// Demo 2: Custom planner demo
	fmt.Println("\n2. Custom Planner Demo")
	runCustomPlannerDemo()

	// Demo 3: Task planner agent demo
	fmt.Println("\n3. Task Planner Agent Demo")
	runTaskPlannerAgentDemo()

	// Demo 4: Complex workflow demo
	fmt.Println("\n4. Complex Workflow Demo")
	runComplexWorkflowDemo()
}

func runBasicAgentDemo() {
	// Create a simple planner -> executor workflow
	planner := workflow.NewNode().
		SetPrep(func(ctx *workflow.WorkContext, params map[string]any) (any, error) {
			goal := ctx.Value("goal").(string)
			return map[string]any{"goal": goal}, nil
		}).
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)

			// Simple planning: create tasks based on keywords
			tasks := []workflow.Task{
				{
					ID:         "task_1",
					Name:       "Execute Goal",
					Type:       "action",
					Action:     "execute",
					Status:     "pending",
					Parameters: map[string]any{"goal": goal},
				},
			}

			plan := &workflow.Plan{
				ID:    "demo_plan_1",
				Name:  "Demo Plan",
				Tasks: tasks,
			}

			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*workflow.Plan)
			ctx.SetValue("plan", plan)
			log.Printf("Created plan with %d tasks", len(plan.Tasks))
			return "plan_ready", nil
		})

	executor := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*workflow.Plan)

			// Execute all tasks
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				task.Result = map[string]any{
					"success": true,
					"message": fmt.Sprintf("Executed: %s", task.Parameters["goal"]),
				}
				log.Printf("Executed task: %s", task.Name)
			}

			plan.Status = "completed"
			return "success", nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			log.Println("All tasks completed successfully")
			return "completed", nil
		})

	// Connect nodes
	planner.Next("plan_ready", executor)

	// Create flow and run
	flow := workflow.NewFlow(planner)
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "automate web form filling")

	finalAction, err := flow.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Workflow completed with action: %s", finalAction)
	}
}

func runCustomPlannerDemo() {
	// Use the example functions from agent_example.go
	workflow.RunCustomPlannerExample()
}

func runTaskPlannerAgentDemo() {
	// Create a task planner agent node
	agent := workflow.NewNode().
		SetPrep(func(ctx *workflow.WorkContext, params map[string]any) (any, error) {
			goal := ctx.Value("goal").(string)
			return map[string]any{"goal": goal}, nil
		}).
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)

			log.Printf("Planning and executing goal: %s", goal)

			// Simulate planning
			plan := &workflow.Plan{
				ID:     "agent_plan",
				Name:   fmt.Sprintf("Agent Plan for: %s", goal),
				Status: "created",
			}

			// Simulate task creation
			if goal == "take screenshot and save" {
				plan.Tasks = []workflow.Task{
					{
						ID:         "task_1",
						Name:       "Take Screenshot",
						Type:       "action",
						Action:     "screenshot",
						Status:     "pending",
						Parameters: map[string]any{},
					},
					{
						ID:           "task_2",
						Name:         "Save File",
						Type:         "action",
						Action:       "save",
						Status:       "pending",
						Parameters:   map[string]any{"filename": "screenshot.png"},
						Dependencies: []string{"task_1"},
					},
				}
			}

			// Simulate execution
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				task.Result = map[string]any{
					"success": true,
					"action":  task.Action,
				}
				log.Printf("Executed: %s", task.Name)
			}

			plan.Status = "completed"
			ctx.SetValue("plan", plan)

			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*workflow.Plan)
			log.Printf("Agent completed plan: %s", plan.Name)
			return "agent_completed", nil
		})

	// Run the agent
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "take screenshot and save")

	action, err := agent.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Agent completed with action: %s", action)
	}
}

func runComplexWorkflowDemo() {
	// Create a more complex workflow with multiple agents

	// Goal analyzer node
	analyzer := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			goal := ctx.Value("goal").(string)

			// Analyze goal complexity
			complexity := "simple"
			if len(goal) > 50 {
				complexity = "complex"
			}

			analysis := map[string]any{
				"goal":       goal,
				"complexity": complexity,
				"keywords":   []string{"click", "type", "wait"},
			}

			ctx.SetValue("analysis", analysis)
			log.Printf("Goal analysis: %s (complexity: %s)", goal, complexity)

			return analysis, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			analysis := execResult.(map[string]any)
			complexity := analysis["complexity"].(string)

			if complexity == "complex" {
				return "complex_planning", nil
			}
			return "simple_planning", nil
		})

	// Simple planner
	simplePlanner := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			analysis := ctx.Value("analysis").(map[string]any)
			goal := analysis["goal"].(string)

			plan := &workflow.Plan{
				ID:   "simple_plan",
				Name: "Simple Plan",
				Tasks: []workflow.Task{
					{
						ID:         "simple_task",
						Name:       "Execute Simple Goal",
						Action:     "execute",
						Status:     "pending",
						Parameters: map[string]any{"goal": goal},
					},
				},
			}

			ctx.SetValue("plan", plan)
			log.Println("Created simple plan")
			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "execute", nil
		})

	// Complex planner
	complexPlanner := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := &workflow.Plan{
				ID:   "complex_plan",
				Name: "Complex Plan",
				Tasks: []workflow.Task{
					{
						ID:     "complex_task_1",
						Name:   "Phase 1",
						Action: "phase1",
						Status: "pending",
					},
					{
						ID:           "complex_task_2",
						Name:         "Phase 2",
						Action:       "phase2",
						Status:       "pending",
						Dependencies: []string{"complex_task_1"},
					},
				},
			}

			ctx.SetValue("plan", plan)
			log.Println("Created complex plan with dependencies")
			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "execute", nil
		})

	// Executor
	executor := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*workflow.Plan)

			// Execute tasks in dependency order
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				log.Printf("Executed: %s", task.Name)
			}

			plan.Status = "completed"
			return "success", nil
		})

	// Connect the workflow
	analyzer.Next("simple_planning", simplePlanner).Next("execute", executor)
	analyzer.Next("complex_planning", complexPlanner).Next("execute", executor)

	// Run the workflow
	flow := workflow.NewFlow(analyzer)
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "This is a very complex goal that requires multiple steps and careful planning to execute properly")

	finalAction, err := flow.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Complex workflow completed with action: %s", finalAction)

		// Show final results
		if plan, ok := ctx.Value("plan").(*workflow.Plan); ok {
			log.Printf("Final plan: %s with %d tasks", plan.Name, len(plan.Tasks))
		}
	}
}
