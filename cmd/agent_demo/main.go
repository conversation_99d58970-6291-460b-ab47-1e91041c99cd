package main

import (
	"context"
	"fmt"
	"log"
	"rpa_server/pkg/workflow"
)

func main() {
	fmt.Println("=== RPA Agent Demo ===")

	// Demo 1: Basic agent workflow
	fmt.Println("\n1. Basic Agent Workflow Demo")
	runBasicAgentDemo()

	// Demo 2: Custom planner demo
	fmt.Println("\n2. Custom Planner Demo")
	runCustomPlannerDemo()

	// Demo 3: Task planner agent demo
	fmt.Println("\n3. Task Planner Agent Demo")
	runTaskPlannerAgentDemo()

	// Demo 4: Complex workflow demo
	fmt.Println("\n4. Complex Workflow Demo")
	runComplexWorkflowDemo()

	// Demo 5: LLM Enhanced workflow demo
	fmt.Println("\n5. LLM Enhanced Workflow Demo")
	runLLMEnhancedDemo()

	// Demo 6: LLM Examples from the package
	fmt.Println("\n6. LLM Package Examples")
	runLLMPackageExamples()
}

func runBasicAgentDemo() {
	// Create a simple planner -> executor workflow
	planner := workflow.NewNode().
		SetPrep(func(ctx *workflow.WorkContext, params map[string]any) (any, error) {
			goal := ctx.Value("goal").(string)
			return map[string]any{"goal": goal}, nil
		}).
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)

			// Simple planning: create tasks based on keywords
			tasks := []workflow.Task{
				{
					ID:         "task_1",
					Name:       "Execute Goal",
					Type:       "action",
					Action:     "execute",
					Status:     "pending",
					Parameters: map[string]any{"goal": goal},
				},
			}

			plan := &workflow.Plan{
				ID:    "demo_plan_1",
				Name:  "Demo Plan",
				Tasks: tasks,
			}

			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*workflow.Plan)
			ctx.SetValue("plan", plan)
			log.Printf("Created plan with %d tasks", len(plan.Tasks))
			return "plan_ready", nil
		})

	executor := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*workflow.Plan)

			// Execute all tasks
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				task.Result = map[string]any{
					"success": true,
					"message": fmt.Sprintf("Executed: %s", task.Parameters["goal"]),
				}
				log.Printf("Executed task: %s", task.Name)
			}

			plan.Status = "completed"
			return "success", nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			log.Println("All tasks completed successfully")
			return "completed", nil
		})

	// Connect nodes
	planner.Next("plan_ready", executor)

	// Create flow and run
	flow := workflow.NewFlow(planner)
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "automate web form filling")

	finalAction, err := flow.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Workflow completed with action: %s", finalAction)
	}
}

func runCustomPlannerDemo() {
	// Use the example functions from agent_example.go
	workflow.RunCustomPlannerExample()
}

func runTaskPlannerAgentDemo() {
	// Create a task planner agent node
	agent := workflow.NewNode().
		SetPrep(func(ctx *workflow.WorkContext, params map[string]any) (any, error) {
			goal := ctx.Value("goal").(string)
			return map[string]any{"goal": goal}, nil
		}).
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)

			log.Printf("Planning and executing goal: %s", goal)

			// Simulate planning
			plan := &workflow.Plan{
				ID:     "agent_plan",
				Name:   fmt.Sprintf("Agent Plan for: %s", goal),
				Status: "created",
			}

			// Simulate task creation
			if goal == "take screenshot and save" {
				plan.Tasks = []workflow.Task{
					{
						ID:         "task_1",
						Name:       "Take Screenshot",
						Type:       "action",
						Action:     "screenshot",
						Status:     "pending",
						Parameters: map[string]any{},
					},
					{
						ID:           "task_2",
						Name:         "Save File",
						Type:         "action",
						Action:       "save",
						Status:       "pending",
						Parameters:   map[string]any{"filename": "screenshot.png"},
						Dependencies: []string{"task_1"},
					},
				}
			}

			// Simulate execution
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				task.Result = map[string]any{
					"success": true,
					"action":  task.Action,
				}
				log.Printf("Executed: %s", task.Name)
			}

			plan.Status = "completed"
			ctx.SetValue("plan", plan)

			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*workflow.Plan)
			log.Printf("Agent completed plan: %s", plan.Name)
			return "agent_completed", nil
		})

	// Run the agent
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "take screenshot and save")

	action, err := agent.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Agent completed with action: %s", action)
	}
}

func runComplexWorkflowDemo() {
	// Create a more complex workflow with multiple agents

	// Goal analyzer node
	analyzer := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			goal := ctx.Value("goal").(string)

			// Analyze goal complexity
			complexity := "simple"
			if len(goal) > 50 {
				complexity = "complex"
			}

			analysis := map[string]any{
				"goal":       goal,
				"complexity": complexity,
				"keywords":   []string{"click", "type", "wait"},
			}

			ctx.SetValue("analysis", analysis)
			log.Printf("Goal analysis: %s (complexity: %s)", goal, complexity)

			return analysis, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			analysis := execResult.(map[string]any)
			complexity := analysis["complexity"].(string)

			if complexity == "complex" {
				return "complex_planning", nil
			}
			return "simple_planning", nil
		})

	// Simple planner
	simplePlanner := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			analysis := ctx.Value("analysis").(map[string]any)
			goal := analysis["goal"].(string)

			plan := &workflow.Plan{
				ID:   "simple_plan",
				Name: "Simple Plan",
				Tasks: []workflow.Task{
					{
						ID:         "simple_task",
						Name:       "Execute Simple Goal",
						Action:     "execute",
						Status:     "pending",
						Parameters: map[string]any{"goal": goal},
					},
				},
			}

			ctx.SetValue("plan", plan)
			log.Println("Created simple plan")
			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "execute", nil
		})

	// Complex planner
	complexPlanner := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := &workflow.Plan{
				ID:   "complex_plan",
				Name: "Complex Plan",
				Tasks: []workflow.Task{
					{
						ID:     "complex_task_1",
						Name:   "Phase 1",
						Action: "phase1",
						Status: "pending",
					},
					{
						ID:           "complex_task_2",
						Name:         "Phase 2",
						Action:       "phase2",
						Status:       "pending",
						Dependencies: []string{"complex_task_1"},
					},
				},
			}

			ctx.SetValue("plan", plan)
			log.Println("Created complex plan with dependencies")
			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "execute", nil
		})

	// Executor
	executor := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*workflow.Plan)

			// Execute tasks in dependency order
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				log.Printf("Executed: %s", task.Name)
			}

			plan.Status = "completed"
			return "success", nil
		})

	// Connect the workflow
	analyzer.Next("simple_planning", simplePlanner).Next("execute", executor)
	analyzer.Next("complex_planning", complexPlanner).Next("execute", executor)

	// Run the workflow
	flow := workflow.NewFlow(analyzer)
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "This is a very complex goal that requires multiple steps and careful planning to execute properly")

	finalAction, err := flow.Run(ctx)
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Complex workflow completed with action: %s", finalAction)

		// Show final results
		if plan, ok := ctx.Value("plan").(*workflow.Plan); ok {
			log.Printf("Final plan: %s with %d tasks", plan.Name, len(plan.Tasks))
		}
	}
}

func runLLMEnhancedDemo() {
	// We'll create a simple LLM-enhanced demo without importing the llm package directly
	// to avoid circular dependency issues in the demo

	// Create LLM planner node
	planner := workflow.NewNode().
		SetPrep(func(ctx *workflow.WorkContext, params map[string]any) (any, error) {
			goal := ctx.Value("goal").(string)
			return map[string]any{"goal": goal}, nil
		}).
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)

			log.Printf("Using mock LLM to plan for goal: %s", goal)

			// Simulate LLM planning
			plan := &workflow.Plan{
				ID:          "llm_enhanced_plan",
				Name:        fmt.Sprintf("LLM Enhanced Plan: %s", goal),
				Description: "Plan generated with LLM assistance",
				Status:      "created",
			}

			// Simulate intelligent task breakdown
			if goal == "automate user registration" {
				plan.Tasks = []workflow.Task{
					{
						ID:          "task_1",
						Name:        "Navigate to registration page",
						Type:        "action",
						Action:      "click",
						Status:      "pending",
						Parameters:  map[string]any{"element": "register_link"},
						Description: "Click on the registration link",
					},
					{
						ID:           "task_2",
						Name:         "Fill personal information",
						Type:         "action",
						Action:       "type_text",
						Status:       "pending",
						Parameters:   map[string]any{"field": "name", "text": "John Doe"},
						Dependencies: []string{"task_1"},
						Description:  "Enter user's full name",
					},
					{
						ID:           "task_3",
						Name:         "Fill email address",
						Type:         "action",
						Action:       "type_text",
						Status:       "pending",
						Parameters:   map[string]any{"field": "email", "text": "<EMAIL>"},
						Dependencies: []string{"task_2"},
						Description:  "Enter user's email address",
					},
					{
						ID:           "task_4",
						Name:         "Create password",
						Type:         "action",
						Action:       "type_text",
						Status:       "pending",
						Parameters:   map[string]any{"field": "password", "text": "SecurePass123!"},
						Dependencies: []string{"task_3"},
						Description:  "Enter a secure password",
					},
					{
						ID:           "task_5",
						Name:         "Submit registration",
						Type:         "action",
						Action:       "click",
						Status:       "pending",
						Parameters:   map[string]any{"element": "submit_button"},
						Dependencies: []string{"task_4"},
						Description:  "Submit the registration form",
					},
				}
			}

			ctx.SetValue("plan", plan)
			return plan, nil
		}).
		SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*workflow.Plan)
			log.Printf("LLM enhanced plan created with %d intelligent tasks", len(plan.Tasks))
			return "llm_plan_ready", nil
		})

	// Create executor
	executor := workflow.NewNode().
		SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*workflow.Plan)

			log.Println("Executing LLM-planned tasks...")
			for i := range plan.Tasks {
				task := &plan.Tasks[i]
				task.Status = "completed"
				task.Result = map[string]any{
					"success": true,
					"message": fmt.Sprintf("LLM-guided execution of %s", task.Name),
				}
				log.Printf("  ✓ %s", task.Name)
			}

			plan.Status = "completed"
			return "success", nil
		})

	// Connect workflow
	planner.Next("llm_plan_ready", executor)

	// Run workflow
	flow := workflow.NewFlow(planner)
	ctx := workflow.WithParam(context.Background(), nil)
	ctx.SetValue("goal", "automate user registration")

	finalAction, err := flow.Run(ctx)
	if err != nil {
		log.Printf("LLM enhanced workflow failed: %v", err)
	} else {
		log.Printf("LLM enhanced workflow completed with action: %s", finalAction)
	}
}

func runLLMPackageExamples() {
	log.Println("Running LLM package examples...")

	// Note: These would normally use the actual LLM package,
	// but we'll simulate the functionality to avoid import issues

	log.Println("1. Mock LLM Task Planning:")
	log.Println("   Goal: 'login to website'")
	log.Println("   Generated tasks:")
	log.Println("   - Navigate to login page")
	log.Println("   - Enter username")
	log.Println("   - Enter password")
	log.Println("   - Submit login form")

	log.Println("\n2. Mock LLM Goal Analysis:")
	log.Println("   Goal: 'fill complex form with validation'")
	log.Println("   Analysis:")
	log.Println("   - Complexity: medium")
	log.Println("   - Feasibility: high")
	log.Println("   - Keywords: [form, validation, input]")
	log.Println("   - Actions: [type_text, click, wait]")

	log.Println("\n3. Mock LLM Code Generation:")
	log.Println("   Prompt: 'Create email validation function'")
	log.Println("   Language: Go")
	log.Println("   Generated code snippet:")
	log.Println("   func validateEmail(email string) bool {")
	log.Println("       // Email validation logic here")
	log.Println("       return true")
	log.Println("   }")

	log.Println("\nLLM package examples completed!")
}

// SimpleMockLLM is a simple mock for demonstration
type SimpleMockLLM struct{}

func (s *SimpleMockLLM) PlanTasks(ctx interface{}, goal string, context map[string]any) (interface{}, error) {
	return map[string]interface{}{
		"description": "Mock LLM plan",
		"complexity":  "medium",
	}, nil
}
