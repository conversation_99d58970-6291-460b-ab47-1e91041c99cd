# RPA SERVER 配置和使用说明

## 1. MCP 客户端配置

```json
{
  "mcpServers": {
    "rpa-server": {
      "command": "/path/to/rpa-mcp-server",
      "args": []
    }
  }
}
```

## 2. 支持的动作

### 鼠标操作
- `click`: 在指定坐标点击
- `double_click`: 在指定坐标双击
- `move_mouse`: 移动鼠标到指定坐标
- `scroll`: 在指定位置滚动

### 键盘操作
- `type_text`: 输入文本
- `key_press`: 按键或组合键

### 屏幕操作
- `screenshot`: 截图
- `get_screen_size`: 获取屏幕尺寸
- `get_mouse_pos`: 获取鼠标位置

### 实用工具
- `wait`: 等待指定时间

## 3. 使用示例

### 点击操作
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "click",
    "arguments": {
      "x": 100,
      "y": 200,
      "button": "left"
    }
  }
}
```

### 输入文本
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "type_text",
    "arguments": {
      "text": "Hello World!"
    }
  }
}
```

### 按键操作
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "key_press",
    "arguments": {
      "key": "ctrl+c"
    }
  }
}
```

### 截图
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "screenshot",
    "arguments": {
      "filename": "my_screenshot.png"
    }
  }
}
```



### 拖拽
**描述**: 从一个坐标拖拽到另一个坐标

**参数**:
- `from_x` (number, 必需): 起始 X 坐标
- `from_y` (number, 必需): 起始 Y 坐标  
- `to_x` (number, 必需): 目标 X 坐标
- `to_y` (number, 必需): 目标 Y 坐标
- `button` (string, 可选): 鼠标按钮，默认为 "left"

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "drag",
    "arguments": {
      "from_x": 100,
      "from_y": 100,
      "to_x": 200,
      "to_y": 200,
      "button": "left"
    }
  }
}
```

### 执行 AppleScript
**描述**: 在 macOS 上执行 AppleScript 代码

**参数**:
- `script` (string, 必需): 要执行的 AppleScript 代码

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "run_apple_script",
    "arguments": {
      "script": "display dialog \"Hello World!\" buttons {\"OK\"}"
    }
  }
}
```

### 打开应用程序
**描述**: 通过应用程序名称打开应用

**参数**:
- `app_name` (string, 必需): 要打开的应用程序名称

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "open_app",
    "arguments": {
      "app_name": "Safari"
    }
  }
}
```

### 获取应用程序列表
**描述**: 获取当前运行的应用程序列表

**参数**: 无

**示例**:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "get_app_list",
    "arguments": {}
  }
}
```

**返回示例**:
```json
{
  "success": true,
  "applications": ["Finder", "Safari", "Terminal", "Chrome"],
  "count": 4,
  "message": "Found 4 running applications"
}
```
