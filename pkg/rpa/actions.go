package actions

import (
	"encoding/json"
	"fmt"
	"image/png"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/go-vgo/robotgo"
)

// MCP Protocol structures
type MCPRequest struct {
	JSONRPC string `json:"jsonrpc"`
	ID      any    `json:"id"`
	Method  string `json:"method"`
	Params  any    `json:"params,omitempty"`
}

type MCPResponse struct {
	JSONRPC string    `json:"jsonrpc"`
	ID      any       `json:"id"`
	Result  any       `json:"result,omitempty"`
	Error   *MCPError `json:"error,omitempty"`
}

type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data,omitempty"`
}

type Tool struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	InputSchema any    `json:"inputSchema"`
}

type ToolCallParams struct {
	Name      string         `json:"name"`
	Arguments map[string]any `json:"arguments"`
}

type ServerInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

type InitializeParams struct {
	ProtocolVersion string         `json:"protocolVersion"`
	Capabilities    map[string]any `json:"capabilities"`
	ClientInfo      map[string]any `json:"clientInfo"`
}

type ServerCapabilities struct {
	Tools map[string]any `json:"tools"`
}

// RPA TOOLS
type RPAMCPServer struct {
	tools     []Tool
	toolFuncs map[string]toolFunc
}

type toolFunc func(map[string]any) (string, error)

// 参数获取辅助函数
func getFloatArg(args map[string]any, key string) (float64, error) {
	v, ok := args[key]
	if !ok {
		return 0, fmt.Errorf("missing %s", key)
	}
	f, ok := v.(float64)
	if !ok {
		return 0, fmt.Errorf("invalid %s", key)
	}
	return f, nil
}

func getStringArg(args map[string]any, key, def string) string {
	v, ok := args[key]
	if !ok {
		return def
	}
	s, ok := v.(string)
	if !ok {
		return def
	}
	return s
}

// marshalResult 用于结构化返回
func marshalResult(v any) string {
	b, _ := json.Marshal(v)
	return string(b)
}

func NewRPAMCPServer() *RPAMCPServer {
	server := &RPAMCPServer{}
	server.initializeTools()
	return server
}

func (s *RPAMCPServer) initializeTools() {
	type toolMeta struct {
		Name        string
		Description string
		InputSchema any
		Func        toolFunc
	}
	metas := []toolMeta{
		{
			Name:        "click",
			Description: "Click at specified coordinates",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"x":      map[string]any{"type": "number", "description": "X coordinate"},
					"y":      map[string]any{"type": "number", "description": "Y coordinate"},
					"button": map[string]any{"type": "string", "description": "Mouse button: left, right, middle", "default": "left"},
				},
				"required": []string{"x", "y"},
			},
			Func: s.click,
		},
		{
			Name:        "double_click",
			Description: "Double click at specified coordinates",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"x": map[string]any{"type": "number", "description": "X coordinate"},
					"y": map[string]any{"type": "number", "description": "Y coordinate"},
				},
				"required": []string{"x", "y"},
			},
			Func: s.doubleClick,
		},
		{
			Name:        "move_mouse",
			Description: "Move mouse to specified coordinates",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"x": map[string]any{"type": "number", "description": "X coordinate"},
					"y": map[string]any{"type": "number", "description": "Y coordinate"},
				},
				"required": []string{"x", "y"},
			},
			Func: s.moveMouse,
		},
		{
			Name:        "type_text",
			Description: "Type text at current cursor position",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"text": map[string]any{"type": "string", "description": "Text to type"},
				},
				"required": []string{"text"},
			},
			Func: s.typeText,
		},
		{
			Name:        "key_press",
			Description: "Press a key or key combination",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"key": map[string]any{"type": "string", "description": "Key to press (e.g., 'enter', 'ctrl+c', 'alt+tab')"},
				},
				"required": []string{"key"},
			},
			Func: s.keyPress,
		},
		{
			Name:        "screenshot",
			Description: "Take a screenshot and save to file",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"filename": map[string]any{"type": "string", "description": "Filename to save screenshot (optional)", "default": "screenshot.png"},
				},
			},
			Func: s.screenshot,
		},
		{
			Name:        "get_screen_size",
			Description: "Get screen dimensions",
			InputSchema: map[string]any{"type": "object", "properties": map[string]any{}},
			Func:        s.getScreenSize,
		},
		{
			Name:        "get_mouse_pos",
			Description: "Get current mouse position",
			InputSchema: map[string]any{"type": "object", "properties": map[string]any{}},
			Func:        s.getMousePos,
		},
		{
			Name:        "scroll",
			Description: "Scroll at specified coordinates",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"x":         map[string]any{"type": "number", "description": "X coordinate"},
					"y":         map[string]any{"type": "number", "description": "Y coordinate"},
					"direction": map[string]any{"type": "string", "description": "Scroll direction: up, down, left, right", "default": "down"},
					"clicks":    map[string]any{"type": "number", "description": "Number of scroll clicks", "default": 3},
				},
				"required": []string{"x", "y"},
			},
			Func: s.scroll,
		},
		{
			Name:        "wait",
			Description: "Wait for specified duration",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"seconds": map[string]any{"type": "number", "description": "Number of seconds to wait"},
				},
				"required": []string{"seconds"},
			},
			Func: s.wait,
		},
		{
			Name:        "drag",
			Description: "Drag from one coordinate to another",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"from_x": map[string]any{"type": "number", "description": "Starting X coordinate"},
					"from_y": map[string]any{"type": "number", "description": "Starting Y coordinate"},
					"to_x":   map[string]any{"type": "number", "description": "Ending X coordinate"},
					"to_y":   map[string]any{"type": "number", "description": "Ending Y coordinate"},
					"button": map[string]any{"type": "string", "description": "Mouse button: left, right, middle", "default": "left"},
				},
				"required": []string{"from_x", "from_y", "to_x", "to_y"},
			},
			Func: s.drag,
		},
		{
			Name:        "run_apple_script",
			Description: "Execute AppleScript code on macOS",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"script": map[string]any{"type": "string", "description": "AppleScript code to execute"},
				},
				"required": []string{"script"},
			},
			Func: s.runAppleScript,
		},
		{
			Name:        "open_app",
			Description: "Open an application by name",
			InputSchema: map[string]any{
				"type": "object",
				"properties": map[string]any{
					"app_name": map[string]any{"type": "string", "description": "Name of the application to open"},
				},
				"required": []string{"app_name"},
			},
			Func: s.openApp,
		},
		{
			Name:        "get_app_list",
			Description: "Get list of running applications",
			InputSchema: map[string]any{"type": "object", "properties": map[string]any{}},
			Func:        s.getAppList,
		},
	}

	s.tools = make([]Tool, 0, len(metas))
	s.toolFuncs = make(map[string]toolFunc, len(metas))
	for _, m := range metas {
		s.tools = append(s.tools, Tool{
			Name:        m.Name,
			Description: m.Description,
			InputSchema: m.InputSchema,
		})
		s.toolFuncs[m.Name] = m.Func
	}
}

func (s *RPAMCPServer) handleRequest(request MCPRequest) MCPResponse {
	log.Printf("Received request: method=%s id=%v", request.Method, request.ID)
	response := MCPResponse{
		JSONRPC: "2.0",
		ID:      request.ID,
	}

	switch request.Method {
	case "initialize":
		log.Printf("Initialize called")
		response.Result = map[string]any{
			"protocolVersion": "2024-11-05",
			"capabilities": ServerCapabilities{
				Tools: map[string]any{},
			},
			"serverInfo": ServerInfo{
				Name:    "RPA TOOLS",
				Version: "1.0.0",
			},
		}

	case "tools/list":
		log.Printf("Tools list requested")
		response.Result = map[string]any{
			"tools": s.tools,
		}

	case "tools/call":
		params, ok := request.Params.(map[string]any)
		if !ok {
			log.Printf("Invalid params for tools/call: %v", request.Params)
			response.Error = &MCPError{
				Code:    -32602,
				Message: "Invalid params",
			}
			return response
		}

		toolParams := ToolCallParams{}
		paramBytes, _ := json.Marshal(params)
		json.Unmarshal(paramBytes, &toolParams)

		log.Printf("Calling tool: %s with args: %+v", toolParams.Name, toolParams.Arguments)
		result, err := s.callTool(toolParams.Name, toolParams.Arguments)
		if err != nil {
			log.Printf("Tool %s execution failed: %v", toolParams.Name, err)
			response.Error = &MCPError{
				Code:    -32603,
				Message: "Tool execution failed",
				Data:    err.Error(),
			}
		} else {
			// 尝试将 result 解析为 JSON 对象
			var obj any
			if err := json.Unmarshal([]byte(result), &obj); err == nil {
				log.Printf("Tool %s executed successfully, structured result: %v", toolParams.Name, obj)
				response.Result = map[string]any{
					"content": []map[string]any{
						{
							"type": "json",
							"data": obj,
						},
					},
				}
			} else {
				log.Printf("Tool %s executed successfully, raw result: %s", toolParams.Name, result)
				response.Result = map[string]any{
					"content": []map[string]any{
						{
							"type": "text",
							"text": result,
						},
					},
				}
			}
		}

	default:
		log.Printf("Unknown method: %s", request.Method)
		response.Error = &MCPError{
			Code:    -32601,
			Message: "Method not found",
		}
	}

	log.Printf("Response: %+v", response)
	return response
}

// 工具注册自动化
func (s *RPAMCPServer) callTool(name string, args map[string]any) (string, error) {
	if s.toolFuncs == nil {
		s.initToolFuncs()
	}
	if fn, ok := s.toolFuncs[name]; ok {
		return fn(args)
	}
	return "", fmt.Errorf("unknown tool: %s", name)
}

func (s *RPAMCPServer) initToolFuncs() {
	s.toolFuncs = map[string]toolFunc{
		"click":            s.click,
		"double_click":     s.doubleClick,
		"move_mouse":       s.moveMouse,
		"type_text":        s.typeText,
		"key_press":        s.keyPress,
		"screenshot":       s.screenshot,
		"get_screen_size":  s.getScreenSize,
		"get_mouse_pos":    s.getMousePos,
		"scroll":           s.scroll,
		"wait":             s.wait,
		"drag":             s.drag,
		"run_apple_script": s.runAppleScript,
		"open_app":         s.openApp,
		"get_app_list":     s.getAppList,
	}
}

func (s *RPAMCPServer) click(args map[string]any) (string, error) {
	x, err := getFloatArg(args, "x")
	if err != nil {
		log.Printf("click: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	y, err := getFloatArg(args, "y")
	if err != nil {
		log.Printf("click: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	button := getStringArg(args, "button", "left")
	log.Printf("click: moving to (%d, %d) and clicking %s", int(x), int(y), button)
	// 显示红色圆圈动画
	// showRedCircle(int(x), int(y))
	robotgo.Move(int(x), int(y))
	robotgo.Click(button, false)
	return marshalResult(map[string]any{
		"success": true,
		"x":       int(x),
		"y":       int(y),
		"button":  button,
		"message": fmt.Sprintf("Clicked at (%d, %d) with %s button", int(x), int(y), button),
	}), nil
}

// // showRedCircle 在指定坐标弹出红色圆圈动画，仅支持 macOS
// func showRedCircle(x, y int) {
// 	applescript := fmt.Sprintf(`
// set px to %d
// set py to %d
// set w to 80
// set h to 80
// set js to "var app = Application.currentApplication();\napp.includeStandardAdditions = true;\nObjC.import('Cocoa');\nvar win = $.NSWindow.alloc.initWithContentRectStyleMaskBackingDefer(\n  $.NSMakeRect(%d, %d, w, h), 15, 2, 0);\nwin.backgroundColor = $.NSColor.clearColor;\nwin.opaque = false;\nwin.level = 1000;\nvar view = $.NSView.alloc.initWithFrame($.NSMakeRect(0,0,w,h));\nvar circle = $.NSBezierPath.bezierPathWithOvalInRect($.NSMakeRect(0,0,w,h));\nvar shape = $.CAShapeLayer.layer;\nshape.path = circle;\nshape.strokeColor = $.NSColor.redColor.CGColor;\nshape.lineWidth = 6;\nshape.fillColor = $.NSColor.clearColor.CGColor;\nview.layer = shape;\nwin.contentView = view;\nwin.makeKeyAndOrderFront(nil);\nObjC.schedule(5, function(){win.close();});"
// do shell script "/usr/bin/osascript -l JavaScript -e '" & js & "'"
// `, x-40, y-40, x-40, y-40)
// 	cmd := exec.Command("osascript", "-e", applescript)
// 	_ = cmd.Start() // 异步执行，不阻塞主流程
// }

func (s *RPAMCPServer) doubleClick(args map[string]any) (string, error) {
	x, err := getFloatArg(args, "x")
	if err != nil {
		log.Printf("doubleClick: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	y, err := getFloatArg(args, "y")
	if err != nil {
		log.Printf("doubleClick: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	log.Printf("doubleClick: moving to (%d, %d) and double clicking", int(x), int(y))
	robotgo.Move(int(x), int(y))
	robotgo.Click("left", true)
	robotgo.MilliSleep(100)
	robotgo.Click("left", true)
	return marshalResult(map[string]any{
		"success": true,
		"x":       int(x),
		"y":       int(y),
		"message": fmt.Sprintf("Double clicked at (%d, %d)", int(x), int(y)),
	}), nil
}

func (s *RPAMCPServer) moveMouse(args map[string]any) (string, error) {
	x, err := getFloatArg(args, "x")
	if err != nil {
		log.Printf("moveMouse: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	y, err := getFloatArg(args, "y")
	if err != nil {
		log.Printf("moveMouse: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	log.Printf("moveMouse: moving to (%d, %d)", int(x), int(y))
	robotgo.Move(int(x), int(y))
	return marshalResult(map[string]any{
		"success": true,
		"x":       int(x),
		"y":       int(y),
		"message": fmt.Sprintf("Moved mouse to (%d, %d)", int(x), int(y)),
	}), nil
}

func (s *RPAMCPServer) typeText(args map[string]any) (string, error) {
	text, ok := args["text"].(string)
	if !ok {
		log.Printf("typeText: invalid text param: %v", args["text"])
		return marshalResult(map[string]any{"success": false, "error": "invalid text"}), nil
	}
	log.Printf("typeText: typing '%s'", text)
	robotgo.TypeStr(text)
	return marshalResult(map[string]any{
		"success": true,
		"text":    text,
		"message": fmt.Sprintf("Typed: %s", text),
	}), nil
}

// 按下，释放
func (s *RPAMCPServer) keyPress(args map[string]any) (string, error) {
	key, ok := args["key"].(string)
	if !ok {
		log.Printf("keyPress: invalid key param: %v", args["key"])
		return marshalResult(map[string]any{"success": false, "error": "invalid key"}), nil
	}
	log.Printf("keyPress: pressing '%s'", key)
	if strings.Contains(key, "+") {
		parts := strings.Split(key, "+")
		if len(parts) == 2 {
			robotgo.KeyTap(parts[1], parts[0])
		} else {
			mainKey := parts[len(parts)-1]
			modifiers := parts[:len(parts)-1]
			switch len(modifiers) {
			case 0:
				robotgo.KeyTap(mainKey)
			case 1:
				robotgo.KeyTap(mainKey, modifiers[0])
			case 2:
				robotgo.KeyTap(mainKey, modifiers[0], modifiers[1])
			case 3:
				robotgo.KeyTap(mainKey, modifiers[0], modifiers[1], modifiers[2])
			default:
				robotgo.KeyTap(mainKey, modifiers[0])
			}
		}
	} else {
		robotgo.KeyTap(key)
	}
	return marshalResult(map[string]any{
		"success": true,
		"key":     key,
		"message": fmt.Sprintf("Pressed key: %s", key),
	}), nil
}

func (s *RPAMCPServer) screenshot(args map[string]any) (string, error) {
	filename := "screenshot.png"
	if f, ok := args["filename"].(string); ok {
		filename = f
	}
	log.Printf("screenshot: capturing to '%s'", filename)
	bitmap := robotgo.CaptureScreen()
	defer robotgo.FreeBitmap(bitmap)

	img := robotgo.ToImage(bitmap)
	file, err := os.Create(filename)
	if err != nil {
		log.Printf("screenshot: failed to create file: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	defer file.Close()

	err = png.Encode(file, img)
	if err != nil {
		log.Printf("screenshot: failed to encode png: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	log.Printf("screenshot: saved to '%s'", filename)
	return marshalResult(map[string]any{"success": true, "filename": filename}), nil
}

func (s *RPAMCPServer) getScreenSize(args map[string]any) (string, error) {
	width, height := robotgo.GetScreenSize()
	log.Printf("getScreenSize: width=%d height=%d", width, height)
	return marshalResult(map[string]any{"width": width, "height": height}), nil
}

func (s *RPAMCPServer) getMousePos(args map[string]any) (string, error) {
	x, y := robotgo.Location()
	log.Printf("getMousePos: x=%d y=%d", x, y)
	return marshalResult(map[string]any{"x": x, "y": y}), nil
}

func (s *RPAMCPServer) scroll(args map[string]any) (string, error) {
	direction := getStringArg(args, "direction", "down")
	clicks := 3
	if c, ok := args["clicks"].(float64); ok {
		clicks = int(c)
	}
	log.Printf("scroll: direction=%s clicks=%d", direction, clicks)
	robotgo.ScrollDir(clicks, direction)
	return marshalResult(map[string]any{
		"success":   true,
		"direction": direction,
		"clicks":    clicks,
		"message":   fmt.Sprintf("Scrolled %s %d clicks", direction, clicks),
	}), nil
}

func (s *RPAMCPServer) wait(args map[string]any) (string, error) {
	seconds, ok := args["seconds"].(float64)
	if !ok {
		log.Printf("wait: invalid seconds param: %v", args["seconds"])
		return marshalResult(map[string]any{"success": false, "error": "invalid seconds"}), nil
	}
	log.Printf("wait: sleeping for %.2f seconds", seconds)
	time.Sleep(time.Duration(seconds * float64(time.Second)))
	return marshalResult(map[string]any{
		"success": true,
		"seconds": seconds,
		"message": fmt.Sprintf("Waited for %.2f seconds", seconds),
	}), nil
}

func (s *RPAMCPServer) drag(args map[string]any) (string, error) {
	fromX, err := getFloatArg(args, "from_x")
	if err != nil {
		log.Printf("drag: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	fromY, err := getFloatArg(args, "from_y")
	if err != nil {
		log.Printf("drag: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	toX, err := getFloatArg(args, "to_x")
	if err != nil {
		log.Printf("drag: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	toY, err := getFloatArg(args, "to_y")
	if err != nil {
		log.Printf("drag: %v", err)
		return marshalResult(map[string]any{"success": false, "error": err.Error()}), nil
	}
	button := getStringArg(args, "button", "left")

	log.Printf("drag: dragging from (%d, %d) to (%d, %d) with %s button", int(fromX), int(fromY), int(toX), int(toY), button)

	// 移动到起始位置
	robotgo.Move(int(fromX), int(fromY))
	// 按下鼠标
	robotgo.Toggle(button, "down")
	// 移动到目标位置
	robotgo.MoveSmooth(int(toX), int(toY))
	// 释放鼠标
	robotgo.Toggle(button, "up")

	return marshalResult(map[string]any{
		"success": true,
		"from_x":  int(fromX),
		"from_y":  int(fromY),
		"to_x":    int(toX),
		"to_y":    int(toY),
		"button":  button,
		"message": fmt.Sprintf("Dragged from (%d, %d) to (%d, %d) with %s button", int(fromX), int(fromY), int(toX), int(toY), button),
	}), nil
}

func (s *RPAMCPServer) runAppleScript(args map[string]any) (string, error) {
	script, ok := args["script"].(string)
	if !ok {
		log.Printf("runAppleScript: invalid script param: %v", args["script"])
		return marshalResult(map[string]any{"success": false, "error": "invalid script"}), nil
	}

	log.Printf("runAppleScript: executing script")
	cmd := exec.Command("osascript", "-e", script)
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("runAppleScript: execution failed: %v", err)
		return marshalResult(map[string]any{
			"success": false,
			"error":   err.Error(),
			"output":  string(output),
		}), nil
	}

	log.Printf("runAppleScript: execution successful")
	return marshalResult(map[string]any{
		"success": true,
		"output":  strings.TrimSpace(string(output)),
		"message": "AppleScript executed successfully",
	}), nil
}

func (s *RPAMCPServer) openApp(args map[string]any) (string, error) {
	appName, ok := args["app_name"].(string)
	if !ok {
		log.Printf("openApp: invalid app_name param: %v", args["app_name"])
		return marshalResult(map[string]any{"success": false, "error": "invalid app_name"}), nil
	}

	log.Printf("openApp: opening application '%s'", appName)

	// 使用 AppleScript 打开应用程序
	script := fmt.Sprintf(`tell application "%s" to activate`, appName)
	cmd := exec.Command("osascript", "-e", script)
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("openApp: failed to open application '%s': %v", appName, err)
		return marshalResult(map[string]any{
			"success":  false,
			"error":    err.Error(),
			"output":   string(output),
			"app_name": appName,
		}), nil
	}

	log.Printf("openApp: successfully opened application '%s'", appName)
	return marshalResult(map[string]any{
		"success":  true,
		"app_name": appName,
		"message":  fmt.Sprintf("Successfully opened application: %s", appName),
	}), nil
}

func (s *RPAMCPServer) getAppList(args map[string]any) (string, error) {
	log.Printf("getAppList: getting applications list")

	// 使用 AppleScript 获取所有应用程序列表
	script := `tell application "System Events" to get name of every application process`
	cmd := exec.Command("osascript", "-e", script)
	output, err := cmd.CombinedOutput()

	if err != nil {
		log.Printf("getAppList: failed to get application list: %v", err)
		return marshalResult(map[string]any{
			"success": false,
			"error":   err.Error(),
			"output":  string(output),
		}), nil
	}

	// 解析输出，AppleScript 返回的格式是 "app1, app2, app3"
	outputStr := strings.TrimSpace(string(output))
	var apps []string
	if outputStr != "" {
		apps = strings.Split(outputStr, ", ")
		// 清理每个应用名称
		for i, app := range apps {
			apps[i] = strings.TrimSpace(app)
		}
	}

	log.Printf("getAppList: found %d  applications", len(apps))
	return marshalResult(map[string]any{
		"success":      true,
		"applications": apps,
		"count":        len(apps),
		"message":      fmt.Sprintf("Found %d applications", len(apps)),
	}), nil
}

// func (s *RPAMCPServer) getRunningAppList(args map[string]any) (string, error) {
// 	log.Printf("getAppList: getting list of running applications")

// 	// 使用 AppleScript 获取运行中的应用程序列表
// 	script := `tell application "System Events" to get name of every application process whose background only is false`
// 	cmd := exec.Command("osascript", "-e", script)
// 	output, err := cmd.CombinedOutput()

// 	if err != nil {
// 		log.Printf("getAppList: failed to get application list: %v", err)
// 		return marshalResult(map[string]any{
// 			"success": false,
// 			"error":   err.Error(),
// 			"output":  string(output),
// 		}), nil
// 	}

// 	// 解析输出，AppleScript 返回的格式是 "app1, app2, app3"
// 	outputStr := strings.TrimSpace(string(output))
// 	var apps []string
// 	if outputStr != "" {
// 		apps = strings.Split(outputStr, ", ")
// 		// 清理每个应用名称
// 		for i, app := range apps {
// 			apps[i] = strings.TrimSpace(app)
// 		}
// 	}

// 	log.Printf("getAppList: found %d running applications", len(apps))
// 	return marshalResult(map[string]any{
// 		"success":      true,
// 		"applications": apps,
// 		"count":        len(apps),
// 		"message":      fmt.Sprintf("Found %d running applications", len(apps)),
// 	}), nil
// }

// 返回所有工具的 JSON 列表
func (s *RPAMCPServer) ToolsListJSON() string {
	b, _ := json.Marshal(s.tools)
	return string(b)
}

// 返回所有工具的美化 JSON 列表
func (s *RPAMCPServer) ToolsListPrettyJSON() string {
	b, _ := json.MarshalIndent(s.tools, "", "  ")
	return string(b)
}

func (s *RPAMCPServer) Exec(jsonStr string) string {
	var request MCPRequest
	err := json.Unmarshal([]byte(jsonStr), &request)
	if err != nil {
		log.Printf("Error decoding request: %v", err)
		return marshalResult(MCPResponse{
			JSONRPC: "2.0",
			ID:      nil,
			Error: &MCPError{
				Code:    -32700,
				Message: "Parse error",
			},
		})
	}
	response := s.handleRequest(request)
	jsonBytes, err := json.Marshal(response)
	if err != nil {
		log.Printf("Error marshaling response: %v", err)
		return marshalResult(MCPResponse{
			JSONRPC: "2.0",
			ID:      request.ID,
			Error: &MCPError{
				Code:    -32603,
				Message: "Internal error",
			},
		})
	}

	return string(jsonBytes)

}

// func main() {
// 	server := NewRPAMCPServer()
// 	// ck := server.ToolsListPrettyJSON()
// 	// println(ck)
// 	// server.click(map[string]any{"x": 500.0, "y": 300.0, "button": "left"})
// 	server.run()
// }
