package workflow

import (
	"context"
	"fmt"
	"log"
	"rpa_server/pkg/llm"
)

// CreateLLMEnhancedWorkflow creates a workflow that uses LLM for intelligent planning
func CreateLLMEnhancedWorkflow(llmClient llm.LLMClient, rpaActionFunc func(*WorkContext, string, map[string]any) (any, error)) BaseNode {
	// Create LLM-enhanced planner
	planner := llmPlannerNode(llmClient)

	// Create executor
	executor := executorNode()
	executor.SetParams(map[string]any{
		"action_func": rpaActionFunc,
	})

	// Create analysis node for goal complexity assessment
	analyzer := NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			goal, _ := ctx.Value("goal").(string)
			if goal == "" {
				return nil, fmt.Errorf("no goal for analysis")
			}
			return goal, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			goal := prepResult.(string)

			// Use LLM to analyze goal
			analysis, err := llmClient.AnalyzeGoal(ctx, goal)
			if err != nil {
				log.Printf("LLM analysis failed, using simple analysis: %v", err)
				// Fallback to simple analysis
				return map[string]any{
					"complexity":  "medium",
					"feasibility": "high",
					"keywords":    []string{},
					"actions":     []string{},
				}, nil
			}

			ctx.SetValue("analysis", analysis)
			return analysis, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			analysis := execResult.(*llm.AnalysisResponse)

			log.Printf("Goal analysis - Complexity: %s, Feasibility: %s",
				analysis.Complexity, analysis.Feasibility)

			if analysis.Feasibility == "low" {
				return "infeasible", nil
			}

			if analysis.Complexity == "complex" {
				return "complex_planning", nil
			}

			return "simple_planning", nil
		})

	// Create result validator
	validator := NewNode().
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			plan := ctx.Value("plan").(*Plan)

			// Validate plan using LLM
			validationPrompt := fmt.Sprintf("Validate this RPA plan for goal '%s': %v",
				plan.Name, plan.Tasks)

			messages := []llm.Message{
				{Role: "system", Content: "You are an RPA expert. Validate plans for feasibility and completeness."},
				{Role: "user", Content: validationPrompt},
			}

			response, err := llmClient.Chat(ctx, messages)
			if err != nil {
				log.Printf("LLM validation failed: %v", err)
				return "validation_skipped", nil
			}

			ctx.SetValue("validation_result", response.Content)
			log.Printf("Plan validation: %s", response.Content)

			return response.Content, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "validated", nil
		})

	// Connect the workflow
	analyzer.Next("simple_planning", planner).Next("plan_ready", validator).Next("validated", executor)
	analyzer.Next("complex_planning", planner) // Same planner for now, but could be different
	analyzer.Next("infeasible", validator)     // Still validate even if marked infeasible

	return analyzer
}

// CreateAdaptiveLLMAgent creates an agent that adapts its behavior based on LLM feedback
func CreateAdaptiveLLMAgent(llmClient llm.LLMClient) BaseNode {
	return NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			goal, _ := ctx.Value("goal").(string)
			if goal == "" {
				return nil, fmt.Errorf("no goal specified")
			}

			// Get execution history if available
			history := ctx.Value("execution_history")

			return map[string]any{
				"goal":    goal,
				"history": history,
			}, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			goal := context["goal"].(string)
			history := context["history"]

			// Create adaptive context based on history
			adaptiveContext := map[string]any{
				"history":  history,
				"adaptive": true,
			}

			if history != nil {
				log.Printf("Using execution history for adaptive planning: %v", history)
			}

			// Get LLM planning
			planResponse, err := llmClient.PlanTasks(ctx, goal, adaptiveContext)

			if err != nil {
				return nil, fmt.Errorf("adaptive planning failed: %w", err)
			}

			// Convert to our plan format
			plan := &Plan{
				ID:          fmt.Sprintf("adaptive_plan_%d", len(goal)),
				Name:        fmt.Sprintf("Adaptive Plan: %s", goal),
				Description: planResponse.Description,
				Status:      "created",
				Context:     map[string]any{"adaptive": true, "llm_generated": true},
			}

			// Convert LLM tasks to our format
			for _, llmTask := range planResponse.Tasks {
				task := Task{
					ID:           llmTask.ID,
					Name:         llmTask.Name,
					Description:  llmTask.Description,
					Type:         llmTask.Type,
					Action:       llmTask.Action,
					Parameters:   llmTask.Parameters,
					Dependencies: llmTask.Dependencies,
					Status:       "pending",
				}
				plan.Tasks = append(plan.Tasks, task)
			}

			ctx.SetValue("plan", plan)
			ctx.SetValue("plan_metadata", map[string]any{
				"complexity": planResponse.Complexity,
				"adaptive":   true,
			})

			return plan, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*Plan)
			log.Printf("Adaptive plan created with %d tasks", len(plan.Tasks))
			return "adaptive_plan_ready", nil
		})
}

// CreateLLMCodeGeneratorNode creates a node that generates code using LLM
func CreateLLMCodeGeneratorNode(llmClient llm.LLMClient) BaseNode {
	return NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			requirement, exists := params["requirement"]
			if !exists {
				return nil, fmt.Errorf("no code requirement specified")
			}

			language, _ := params["language"].(string)
			if language == "" {
				language = "go" // Default language
			}

			return map[string]any{
				"requirement": requirement,
				"language":    language,
			}, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			requirement := context["requirement"].(string)
			language := context["language"].(string)

			// Generate code using LLM
			codeResponse, err := llmClient.GenerateCode(ctx, requirement, language)
			if err != nil {
				return nil, fmt.Errorf("code generation failed: %w", err)
			}

			ctx.SetValue("generated_code", codeResponse.Code)
			ctx.SetValue("code_explanation", codeResponse.Explanation)

			return codeResponse, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			codeResponse := execResult.(*llm.CodeResponse)
			log.Printf("Generated %s code: %d characters", codeResponse.Language, len(codeResponse.Code))
			return "code_generated", nil
		})
}

// RunLLMEnhancedExample demonstrates the LLM-enhanced workflow
func RunLLMEnhancedExample() {
	// Create LLM client (using mock for demo)
	llmClient := llm.NewMockLLMClient()

	// Create RPA action function
	rpaActionFunc := func(ctx *WorkContext, action string, params map[string]any) (any, error) {
		log.Printf("Executing RPA action: %s with params: %v", action, params)

		switch action {
		case "click":
			return map[string]any{"success": true, "action": "click"}, nil
		case "type_text":
			return map[string]any{"success": true, "action": "type_text"}, nil
		case "wait":
			return map[string]any{"success": true, "action": "wait"}, nil
		default:
			return map[string]any{"success": true, "action": action}, nil
		}
	}

	// Create LLM-enhanced workflow
	workflow := CreateLLMEnhancedWorkflow(llmClient, rpaActionFunc)

	// Run the workflow
	ctx := WithParam(context.Background(), nil)
	ctx.SetValue("goal", "login to website and navigate to dashboard")

	log.Println("Starting LLM-enhanced workflow...")
	finalAction, err := workflow.Run(ctx)
	if err != nil {
		log.Printf("Workflow failed: %v", err)
		return
	}

	log.Printf("LLM-enhanced workflow completed with action: %s", finalAction)

	// Show results
	if plan, ok := ctx.Value("plan").(*Plan); ok {
		log.Printf("Final plan: %s", plan.Name)
		log.Printf("Plan description: %s", plan.Description)
		log.Printf("Tasks executed: %d", len(plan.Tasks))

		for _, task := range plan.Tasks {
			log.Printf("  - %s: %s (Status: %s)", task.ID, task.Name, task.Status)
		}
	}

	if validation, ok := ctx.Value("validation_result").(string); ok {
		log.Printf("LLM Validation: %s", validation)
	}
}

// RunAdaptiveLLMExample demonstrates the adaptive LLM agent
func RunAdaptiveLLMExample() {
	llmClient := llm.NewMockLLMClient()

	// Create adaptive agent
	agent := CreateAdaptiveLLMAgent(llmClient)

	// Simulate execution history
	executionHistory := map[string]any{
		"previous_attempts":  2,
		"failed_actions":     []string{"click_wrong_element"},
		"successful_actions": []string{"type_text", "wait"},
		"lessons_learned":    "Need to wait longer for page load",
	}

	ctx := WithParam(context.Background(), nil)
	ctx.SetValue("goal", "fill registration form")
	ctx.SetValue("execution_history", executionHistory)

	log.Println("Starting adaptive LLM agent...")
	action, err := agent.Run(ctx)
	if err != nil {
		log.Printf("Adaptive agent failed: %v", err)
		return
	}

	log.Printf("Adaptive agent completed with action: %s", action)

	if plan, ok := ctx.Value("plan").(*Plan); ok {
		log.Printf("Adaptive plan: %s", plan.Name)
		log.Printf("Description: %s", plan.Description)

		for _, task := range plan.Tasks {
			log.Printf("  - %s: %s", task.ID, task.Name)
		}
	}
}

// RunCodeGenerationExample demonstrates LLM code generation
func RunCodeGenerationExample() {
	llmClient := llm.NewMockLLMClient()

	// Create code generator
	codeGen := CreateLLMCodeGeneratorNode(llmClient)
	codeGen.SetParams(map[string]any{
		"requirement": "Create a function that validates email addresses",
		"language":    "go",
	})

	ctx := WithParam(context.Background(), nil)

	log.Println("Starting code generation...")
	action, err := codeGen.Run(ctx)
	if err != nil {
		log.Printf("Code generation failed: %v", err)
		return
	}

	log.Printf("Code generation completed with action: %s", action)

	if code, ok := ctx.Value("generated_code").(string); ok {
		log.Printf("Generated code:\n%s", code)
	}

	if explanation, ok := ctx.Value("code_explanation").(string); ok {
		log.Printf("Explanation: %s", explanation)
	}
}
