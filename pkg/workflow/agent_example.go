package workflow

import (
	"context"
	"fmt"
	"log"
)

// Example: How to use the agent nodes in a real RPA scenario

// CreateRPAAgentWorkflow creates a complete RPA workflow with planning and execution
func CreateRPAAgentWorkflow(rpaActionFunc func(*WorkContext, string, map[string]any) (any, error)) BaseNode {
	// Create planner node
	planner := plannerNode()

	// Create executor node with RPA action function
	executor := executorNode()
	executor.SetParams(map[string]any{
		"action_func": rpaActionFunc,
	})

	// Create result capture node
	resultCapture := NewNode().
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			// Capture final results
			plan := ctx.Value("plan").(*Plan)
			results := make(map[string]any)

			for _, task := range plan.Tasks {
				results[task.ID] = map[string]any{
					"name":   task.Name,
					"status": task.Status,
					"result": task.Result,
				}
			}

			ctx.SetValue("final_results", results)
			log.Printf("Captured results for %d tasks", len(plan.Tasks))
			return results, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			return "results_captured", nil
		})

	// Connect the workflow: planner -> executor -> resultCapture
	planner.Next("plan_ready", executor).
		Next("completed", resultCapture).
		Next("continue", executor) // Loop back for multi-step execution

	// Handle failure cases
	executor.Next("failed", resultCapture)

	return planner
}

// CreateCustomPlannerWorkflow creates a workflow with custom planning logic
func CreateCustomPlannerWorkflow(
	customPlanFunc func(string, map[string]any) (*Plan, error),
	rpaActionFunc func(*WorkContext, string, map[string]any) (any, error),
) BaseNode {
	// Create planner with custom planning function
	planner := plannerNode()
	planner.SetParams(map[string]any{
		"plan_func": customPlanFunc,
	})

	// Create executor
	executor := executorNode()
	executor.SetParams(map[string]any{
		"action_func": rpaActionFunc,
	})

	// Connect nodes
	planner.Next("plan_ready", executor)

	return planner
}

// Example RPA action function that integrates with the RPA server
func ExampleRPAActionFunc(ctx *WorkContext, action string, params map[string]any) (any, error) {
	log.Printf("Executing RPA action: %s with params: %v", action, params)

	switch action {
	case "click":
		x, _ := params["x"].(float64)
		y, _ := params["y"].(float64)
		if x == 0 || y == 0 {
			// Use default coordinates if not specified
			x, y = 100, 100
		}
		return map[string]any{
			"success": true,
			"action":  "click",
			"x":       x,
			"y":       y,
			"message": fmt.Sprintf("Clicked at (%.0f, %.0f)", x, y),
		}, nil

	case "type_text":
		text, _ := params["text"].(string)
		if text == "" {
			text = "Hello World" // Default text
		}
		return map[string]any{
			"success": true,
			"action":  "type_text",
			"text":    text,
			"message": fmt.Sprintf("Typed: %s", text),
		}, nil

	case "wait":
		duration, _ := params["duration"].(float64)
		if duration == 0 {
			duration = 1000 // Default 1 second
		}
		return map[string]any{
			"success":  true,
			"action":   "wait",
			"duration": duration,
			"message":  fmt.Sprintf("Waited %.0f ms", duration),
		}, nil

	case "screenshot":
		return map[string]any{
			"success": true,
			"action":  "screenshot",
			"message": "Screenshot taken",
		}, nil

	case "generic":
		goal, _ := params["goal"].(string)
		return map[string]any{
			"success": true,
			"action":  "generic",
			"goal":    goal,
			"message": fmt.Sprintf("Executed generic action for goal: %s", goal),
		}, nil

	default:
		return nil, fmt.Errorf("unknown action: %s", action)
	}
}

// Example custom planning function for complex scenarios
func ExampleCustomPlanFunc(goal string, context map[string]any) (*Plan, error) {
	plan := &Plan{
		ID:          fmt.Sprintf("custom_plan_%d", len(goal)),
		Name:        fmt.Sprintf("Custom Plan for: %s", goal),
		Description: fmt.Sprintf("Custom planning logic for: %s", goal),
		Status:      "created",
		Context:     context,
	}

	// Custom planning logic based on goal analysis
	if goal == "login to website" {
		plan.Tasks = []Task{
			{
				ID:          "task_1",
				Name:        "Navigate to login page",
				Type:        "action",
				Action:      "click",
				Status:      "pending",
				Parameters:  map[string]any{"x": 200, "y": 100},
				Description: "Click on login button",
			},
			{
				ID:           "task_2",
				Name:         "Enter username",
				Type:         "action",
				Action:       "type_text",
				Status:       "pending",
				Parameters:   map[string]any{"text": "<EMAIL>"},
				Dependencies: []string{"task_1"},
				Description:  "Type username in input field",
			},
			{
				ID:           "task_3",
				Name:         "Enter password",
				Type:         "action",
				Action:       "type_text",
				Status:       "pending",
				Parameters:   map[string]any{"text": "password123"},
				Dependencies: []string{"task_2"},
				Description:  "Type password in input field",
			},
			{
				ID:           "task_4",
				Name:         "Submit login",
				Type:         "action",
				Action:       "click",
				Status:       "pending",
				Parameters:   map[string]any{"x": 300, "y": 400},
				Dependencies: []string{"task_3"},
				Description:  "Click submit button",
			},
		}
	} else if goal == "fill form and submit" {
		plan.Tasks = []Task{
			{
				ID:         "task_1",
				Name:       "Fill name field",
				Type:       "action",
				Action:     "type_text",
				Status:     "pending",
				Parameters: map[string]any{"text": "John Doe"},
			},
			{
				ID:           "task_2",
				Name:         "Fill email field",
				Type:         "action",
				Action:       "type_text",
				Status:       "pending",
				Parameters:   map[string]any{"text": "<EMAIL>"},
				Dependencies: []string{"task_1"},
			},
			{
				ID:           "task_3",
				Name:         "Submit form",
				Type:         "action",
				Action:       "click",
				Status:       "pending",
				Parameters:   map[string]any{"x": 250, "y": 350},
				Dependencies: []string{"task_2"},
			},
		}
	} else {
		// Fallback to simple planning
		plan.Tasks = parseGoalIntoTasks(goal)
	}

	return plan, nil
}

// RunRPAAgentExample demonstrates how to use the agent in practice
func RunRPAAgentExample() {
	// Create the workflow
	workflow := CreateRPAAgentWorkflow(ExampleRPAActionFunc)

	// Create context and set goal
	ctx := WithParam(context.Background(), nil)
	ctx.SetValue("goal", "click button and type hello")

	// Run the workflow
	log.Println("Starting RPA Agent workflow...")
	finalAction, err := workflow.Run(ctx)
	if err != nil {
		log.Printf("Workflow failed: %v", err)
		return
	}

	log.Printf("Workflow completed with action: %s", finalAction)

	// Check results
	if plan, ok := ctx.Value("plan").(*Plan); ok {
		log.Printf("Final plan status: %s", plan.Status)
		log.Printf("Executed %d tasks", len(plan.Tasks))

		for _, task := range plan.Tasks {
			log.Printf("Task %s: %s (Status: %s)", task.ID, task.Name, task.Status)
			if task.Result != nil {
				log.Printf("  Result: %v", task.Result)
			}
		}
	}
}

// RunCustomPlannerExample demonstrates custom planning
func RunCustomPlannerExample() {
	// Create workflow with custom planner
	workflow := CreateCustomPlannerWorkflow(ExampleCustomPlanFunc, ExampleRPAActionFunc)

	// Create context and set goal
	ctx := WithParam(context.Background(), nil)
	ctx.SetValue("goal", "login to website")

	// Run the workflow
	log.Println("Starting custom planner workflow...")
	finalAction, err := workflow.Run(ctx)
	if err != nil {
		log.Printf("Workflow failed: %v", err)
		return
	}

	log.Printf("Custom workflow completed with action: %s", finalAction)

	// Check results
	if plan, ok := ctx.Value("plan").(*Plan); ok {
		log.Printf("Plan: %s", plan.Name)
		log.Printf("Tasks created: %d", len(plan.Tasks))

		for _, task := range plan.Tasks {
			log.Printf("Task %s: %s -> %v", task.ID, task.Name, task.Dependencies)
		}
	}
}
