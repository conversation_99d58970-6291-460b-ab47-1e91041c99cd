package workflow

import (
	"fmt"
	"log"
	"time"
)

// Task represents a single task in the plan
type Task struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Type         string         `json:"type"` // "action", "condition", "parallel", "sequential"
	Action       string         `json:"action,omitempty"`
	Parameters   map[string]any `json:"parameters,omitempty"`
	Dependencies []string       `json:"dependencies,omitempty"`
	SubTasks     []Task         `json:"sub_tasks,omitempty"`
	Status       string         `json:"status"` // "pending", "running", "completed", "failed"
	Result       any            `json:"result,omitempty"`
	Error        string         `json:"error,omitempty"`
	StartTime    *time.Time     `json:"start_time,omitempty"`
	EndTime      *time.Time     `json:"end_time,omitempty"`
}

// Plan represents a complete execution plan
type Plan struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Tasks       []Task         `json:"tasks"`
	Context     map[string]any `json:"context"`
	Status      string         `json:"status"` // "created", "running", "completed", "failed"
	CreatedAt   time.Time      `json:"created_at"`
	StartedAt   *time.Time     `json:"started_at,omitempty"`
	CompletedAt *time.Time     `json:"completed_at,omitempty"`
}

// TaskExecutor defines the interface for executing different types of tasks
type TaskExecutor interface {
	Execute(ctx *WorkContext, task *Task) (any, error)
	CanHandle(taskType string) bool
}

// PlannerAgent is responsible for decomposing high-level goals into executable tasks
type PlannerAgent struct {
	nodeCore
	MaxRetries   int
	WaitDuration time.Duration

	// User-defined functions
	PlanFunc     func(ctx *WorkContext, params map[string]any) (*Plan, error)
	ValidateFunc func(ctx *WorkContext, plan *Plan) error
}

// NewPlannerAgent creates a new PlannerAgent
func NewPlannerAgent() *PlannerAgent {
	pa := &PlannerAgent{
		MaxRetries:   3,
		WaitDuration: time.Second,
		PlanFunc: func(ctx *WorkContext, params map[string]any) (*Plan, error) {
			return nil, fmt.Errorf("PlanFunc not implemented")
		},
		ValidateFunc: func(ctx *WorkContext, plan *Plan) error {
			return nil // Default: no validation
		},
	}
	pa.initCore()
	return pa
}

// SetPlanFunc sets the planning function
func (pa *PlannerAgent) SetPlanFunc(f func(ctx *WorkContext, params map[string]any) (*Plan, error)) *PlannerAgent {
	pa.PlanFunc = f
	return pa
}

// SetValidateFunc sets the validation function
func (pa *PlannerAgent) SetValidateFunc(f func(ctx *WorkContext, plan *Plan) error) *PlannerAgent {
	pa.ValidateFunc = f
	return pa
}

// Prep prepares the planning context
func (pa *PlannerAgent) Prep(ctx *WorkContext) (any, error) {
	// Extract goal and requirements from context or parameters
	goal, _ := ctx.Value("goal").(string)
	if goal == "" {
		if goalParam, exists := pa.params["goal"]; exists {
			goal, _ = goalParam.(string)
		}
	}

	if goal == "" {
		return nil, fmt.Errorf("no goal specified for planning")
	}

	planningContext := map[string]any{
		"goal":        goal,
		"constraints": ctx.Value("constraints"),
		"resources":   ctx.Value("resources"),
		"deadline":    ctx.Value("deadline"),
	}

	return planningContext, nil
}

// Exec executes the planning process
func (pa *PlannerAgent) Exec(ctx *WorkContext, prepResult any) (any, error) {
	planningContext, ok := prepResult.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("invalid planning context")
	}

	// Create plan using the user-defined function
	plan, err := pa.PlanFunc(ctx, planningContext)
	if err != nil {
		return nil, fmt.Errorf("planning failed: %w", err)
	}

	// Validate the plan
	if err := pa.ValidateFunc(ctx, plan); err != nil {
		return nil, fmt.Errorf("plan validation failed: %w", err)
	}

	// Set plan metadata
	now := time.Now()
	plan.CreatedAt = now
	plan.Status = "created"

	return plan, nil
}

// Post processes the planning result
func (pa *PlannerAgent) Post(ctx *WorkContext, prepResult any, execResult any) (string, error) {
	plan, ok := execResult.(*Plan)
	if !ok {
		return "", fmt.Errorf("invalid plan result")
	}

	// Store the plan in context for downstream nodes
	ctx.SetValue("plan", plan)
	ctx.SetValue("plan_id", plan.ID)

	log.Printf("Plan created: %s with %d tasks", plan.Name, len(plan.Tasks))

	return DefaultAction, nil
}

// Run executes the planner agent
func (pa *PlannerAgent) Run(ctx *WorkContext) (string, error) {
	return pa.InternalRun(ctx)
}

// InternalRun implements the BaseNode interface
func (pa *PlannerAgent) InternalRun(ctx *WorkContext) (string, error) {
	prepRes, err := pa.Prep(ctx)
	if err != nil {
		return "", newWorkFlowError(fmt.Sprintf("Prep phase failed in %T", pa), err)
	}

	var execRes any
	var lastExecErr error

	for retry := 0; retry < pa.MaxRetries; retry++ {
		execRes, lastExecErr = pa.Exec(ctx, prepRes)
		if lastExecErr == nil {
			break
		}
		if retry < pa.MaxRetries-1 && pa.WaitDuration > 0 {
			time.Sleep(pa.WaitDuration)
		}
	}

	if lastExecErr != nil {
		return "", newWorkFlowError(fmt.Sprintf("Exec phase failed in %T after %d retries", pa, pa.MaxRetries), lastExecErr)
	}

	action, err := pa.Post(ctx, prepRes, execRes)
	if err != nil {
		return "", newWorkFlowError(fmt.Sprintf("Post phase failed in %T", pa), err)
	}

	return action, nil
}

// ExecutorAgent is responsible for executing tasks from a plan
type ExecutorAgent struct {
	nodeCore
	MaxRetries   int
	WaitDuration time.Duration

	// Task executors for different task types
	executors map[string]TaskExecutor

	// User-defined functions
	PreExecuteFunc  func(ctx *WorkContext, task *Task) error
	PostExecuteFunc func(ctx *WorkContext, task *Task, result any, err error) error
}

// NewExecutorAgent creates a new ExecutorAgent
func NewExecutorAgent() *ExecutorAgent {
	ea := &ExecutorAgent{
		MaxRetries:   3,
		WaitDuration: time.Second,
		executors:    make(map[string]TaskExecutor),
		PreExecuteFunc: func(ctx *WorkContext, task *Task) error {
			return nil // Default: no pre-execution logic
		},
		PostExecuteFunc: func(ctx *WorkContext, task *Task, result any, err error) error {
			return nil // Default: no post-execution logic
		},
	}
	ea.initCore()
	return ea
}

// RegisterExecutor registers a task executor for a specific task type
func (ea *ExecutorAgent) RegisterExecutor(taskType string, executor TaskExecutor) *ExecutorAgent {
	ea.executors[taskType] = executor
	return ea
}

// SetPreExecuteFunc sets the pre-execution function
func (ea *ExecutorAgent) SetPreExecuteFunc(f func(ctx *WorkContext, task *Task) error) *ExecutorAgent {
	ea.PreExecuteFunc = f
	return ea
}

// SetPostExecuteFunc sets the post-execution function
func (ea *ExecutorAgent) SetPostExecuteFunc(f func(ctx *WorkContext, task *Task, result any, err error) error) *ExecutorAgent {
	ea.PostExecuteFunc = f
	return ea
}

// Prep prepares the execution context
func (ea *ExecutorAgent) Prep(ctx *WorkContext) (any, error) {
	// Get the plan from context
	plan, ok := ctx.Value("plan").(*Plan)
	if !ok {
		return nil, fmt.Errorf("no plan found in context")
	}

	// Get specific task to execute (if specified)
	taskID, _ := ctx.Value("task_id").(string)
	if taskID == "" {
		if taskIDParam, exists := ea.params["task_id"]; exists {
			taskID, _ = taskIDParam.(string)
		}
	}

	executionContext := map[string]any{
		"plan":    plan,
		"task_id": taskID,
	}

	return executionContext, nil
}

// Exec executes the task(s)
func (ea *ExecutorAgent) Exec(ctx *WorkContext, prepResult any) (any, error) {
	execContext, ok := prepResult.(map[string]any)
	if !ok {
		return nil, fmt.Errorf("invalid execution context")
	}

	plan := execContext["plan"].(*Plan)
	taskID, _ := execContext["task_id"].(string)

	if taskID != "" {
		// Execute specific task
		task := ea.findTaskByID(plan, taskID)
		if task == nil {
			return nil, fmt.Errorf("task not found: %s", taskID)
		}

		result, err := ea.executeTask(ctx, task)
		return map[string]any{
			"task_id": taskID,
			"result":  result,
			"error":   err,
		}, nil
	} else {
		// Execute all ready tasks
		results, err := ea.executeReadyTasks(ctx, plan)
		return map[string]any{
			"results": results,
			"error":   err,
		}, nil
	}
}

// Post processes the execution result
func (ea *ExecutorAgent) Post(ctx *WorkContext, prepResult any, execResult any) (string, error) {
	result, ok := execResult.(map[string]any)
	if !ok {
		return "", fmt.Errorf("invalid execution result")
	}

	if err, exists := result["error"]; exists && err != nil {
		log.Printf("Task execution failed: %v", err)
		return "failed", nil
	}

	// Update plan status in context
	execContext := prepResult.(map[string]any)
	plan := execContext["plan"].(*Plan)

	// Check if all tasks are completed
	allCompleted := true
	for _, task := range plan.Tasks {
		if task.Status != "completed" {
			allCompleted = false
			break
		}
	}

	if allCompleted {
		plan.Status = "completed"
		now := time.Now()
		plan.CompletedAt = &now
		ctx.SetValue("plan", plan)
		log.Printf("Plan completed: %s", plan.Name)
		return "completed", nil
	}

	return DefaultAction, nil
}

// Run executes the executor agent
func (ea *ExecutorAgent) Run(ctx *WorkContext) (string, error) {
	return ea.InternalRun(ctx)
}

// InternalRun implements the BaseNode interface
func (ea *ExecutorAgent) InternalRun(ctx *WorkContext) (string, error) {
	prepRes, err := ea.Prep(ctx)
	if err != nil {
		return "", newWorkFlowError(fmt.Sprintf("Prep phase failed in %T", ea), err)
	}

	var execRes any
	var lastExecErr error

	for retry := 0; retry < ea.MaxRetries; retry++ {
		execRes, lastExecErr = ea.Exec(ctx, prepRes)
		if lastExecErr == nil {
			break
		}
		if retry < ea.MaxRetries-1 && ea.WaitDuration > 0 {
			time.Sleep(ea.WaitDuration)
		}
	}

	if lastExecErr != nil {
		return "", newWorkFlowError(fmt.Sprintf("Exec phase failed in %T after %d retries", ea, ea.MaxRetries), lastExecErr)
	}

	action, err := ea.Post(ctx, prepRes, execRes)
	if err != nil {
		return "", newWorkFlowError(fmt.Sprintf("Post phase failed in %T", ea), err)
	}

	return action, nil
}

// Helper methods for ExecutorAgent

// findTaskByID finds a task by its ID in the plan
func (ea *ExecutorAgent) findTaskByID(plan *Plan, taskID string) *Task {
	for i := range plan.Tasks {
		if plan.Tasks[i].ID == taskID {
			return &plan.Tasks[i]
		}
		// Search in subtasks recursively
		if found := ea.findTaskInSubTasks(plan.Tasks[i].SubTasks, taskID); found != nil {
			return found
		}
	}
	return nil
}

// findTaskInSubTasks recursively searches for a task in subtasks
func (ea *ExecutorAgent) findTaskInSubTasks(subTasks []Task, taskID string) *Task {
	for i := range subTasks {
		if subTasks[i].ID == taskID {
			return &subTasks[i]
		}
		if found := ea.findTaskInSubTasks(subTasks[i].SubTasks, taskID); found != nil {
			return found
		}
	}
	return nil
}

// executeTask executes a single task
func (ea *ExecutorAgent) executeTask(ctx *WorkContext, task *Task) (any, error) {
	// Pre-execution hook
	if err := ea.PreExecuteFunc(ctx, task); err != nil {
		return nil, fmt.Errorf("pre-execution failed: %w", err)
	}

	// Update task status
	now := time.Now()
	task.Status = "running"
	task.StartTime = &now

	log.Printf("Executing task: %s (%s)", task.Name, task.Type)

	// Find appropriate executor
	executor, exists := ea.executors[task.Type]
	if !exists {
		task.Status = "failed"
		task.Error = fmt.Sprintf("no executor found for task type: %s", task.Type)
		endTime := time.Now()
		task.EndTime = &endTime
		return nil, fmt.Errorf(task.Error)
	}

	// Execute the task
	result, err := executor.Execute(ctx, task)

	// Update task status and result
	endTime := time.Now()
	task.EndTime = &endTime

	if err != nil {
		task.Status = "failed"
		task.Error = err.Error()
	} else {
		task.Status = "completed"
		task.Result = result
	}

	// Post-execution hook
	if hookErr := ea.PostExecuteFunc(ctx, task, result, err); hookErr != nil {
		log.Printf("Post-execution hook failed: %v", hookErr)
	}

	return result, err
}

// executeReadyTasks executes all tasks that are ready to run
func (ea *ExecutorAgent) executeReadyTasks(ctx *WorkContext, plan *Plan) (map[string]any, error) {
	results := make(map[string]any)
	var lastError error

	// Update plan status
	if plan.Status == "created" {
		plan.Status = "running"
		now := time.Now()
		plan.StartedAt = &now
	}

	for i := range plan.Tasks {
		task := &plan.Tasks[i]
		if ea.isTaskReady(plan, task) {
			result, err := ea.executeTask(ctx, task)
			results[task.ID] = map[string]any{
				"result": result,
				"error":  err,
			}
			if err != nil {
				lastError = err
			}
		}
	}

	return results, lastError
}

// isTaskReady checks if a task is ready to execute (all dependencies completed)
func (ea *ExecutorAgent) isTaskReady(plan *Plan, task *Task) bool {
	if task.Status != "pending" {
		return false
	}

	// Check if all dependencies are completed
	for _, depID := range task.Dependencies {
		depTask := ea.findTaskByID(plan, depID)
		if depTask == nil || depTask.Status != "completed" {
			return false
		}
	}

	return true
}
