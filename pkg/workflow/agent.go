package workflow

import (
	"fmt"
	"log"
	"strings"
	"time"
)

// Task represents a single task in the plan
type Task struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Type         string         `json:"type"` // "action", "condition", "parallel", "sequential"
	Action       string         `json:"action,omitempty"`
	Parameters   map[string]any `json:"parameters,omitempty"`
	Dependencies []string       `json:"dependencies,omitempty"`
	SubTasks     []Task         `json:"sub_tasks,omitempty"`
	Status       string         `json:"status"` // "pending", "running", "completed", "failed"
	Result       any            `json:"result,omitempty"`
	Error        string         `json:"error,omitempty"`
	StartTime    *time.Time     `json:"start_time,omitempty"`
	EndTime      *time.Time     `json:"end_time,omitempty"`
}

// Plan represents a complete execution plan
type Plan struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Tasks       []Task         `json:"tasks"`
	Context     map[string]any `json:"context"`
	Status      string         `json:"status"` // "created", "running", "completed", "failed"
	CreatedAt   time.Time      `json:"created_at"`
	StartedAt   *time.Time     `json:"started_at,omitempty"`
	CompletedAt *time.Time     `json:"completed_at,omitempty"`
}

// --- Agent Node Factory Functions (Following test style) ---

// plannerNode creates a node that decomposes a goal into executable tasks
func plannerNode() BaseNode {
	return NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			// Extract goal from context or parameters
			goal, _ := ctx.Value("goal").(string)
			if goal == "" {
				if goalParam, exists := params["goal"]; exists {
					goal, _ = goalParam.(string)
				}
			}

			if goal == "" {
				return nil, fmt.Errorf("no goal specified for planning")
			}

			return map[string]any{
				"goal":        goal,
				"constraints": ctx.Value("constraints"),
				"resources":   ctx.Value("resources"),
				"deadline":    ctx.Value("deadline"),
			}, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			planningContext := prepResult.(map[string]any)
			goal := planningContext["goal"].(string)

			// Simple task decomposition logic (can be customized via params)
			if planFunc, exists := params["plan_func"]; exists {
				if f, ok := planFunc.(func(string, map[string]any) (*Plan, error)); ok {
					return f(goal, planningContext)
				}
			}

			// Default planning logic - decompose goal into basic tasks
			plan := &Plan{
				ID:          fmt.Sprintf("plan_%d", time.Now().Unix()),
				Name:        fmt.Sprintf("Plan for: %s", goal),
				Description: fmt.Sprintf("Auto-generated plan to achieve: %s", goal),
				Status:      "created",
				CreatedAt:   time.Now(),
				Context:     planningContext,
			}

			// Simple goal parsing - split by keywords
			tasks := parseGoalIntoTasks(goal)
			plan.Tasks = tasks

			return plan, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			plan := execResult.(*Plan)

			// Store plan in context for downstream nodes
			ctx.SetValue("plan", plan)
			ctx.SetValue("plan_id", plan.ID)

			log.Printf("Plan created: %s with %d tasks", plan.Name, len(plan.Tasks))

			if len(plan.Tasks) == 0 {
				return "no_tasks", nil
			}

			return "plan_ready", nil
		})
}

// parseGoalIntoTasks is a simple goal parser that creates tasks based on keywords
func parseGoalIntoTasks(goal string) []Task {
	var tasks []Task
	taskID := 1

	goal = strings.ToLower(goal)

	// Simple keyword-based task generation
	if strings.Contains(goal, "click") {
		tasks = append(tasks, Task{
			ID:          fmt.Sprintf("task_%d", taskID),
			Name:        "Click Action",
			Description: "Perform click action",
			Type:        "action",
			Action:      "click",
			Status:      "pending",
			Parameters:  map[string]any{"action_type": "click"},
		})
		taskID++
	}

	if strings.Contains(goal, "type") || strings.Contains(goal, "input") {
		tasks = append(tasks, Task{
			ID:          fmt.Sprintf("task_%d", taskID),
			Name:        "Type Text",
			Description: "Type text input",
			Type:        "action",
			Action:      "type_text",
			Status:      "pending",
			Parameters:  map[string]any{"action_type": "type_text"},
		})
		taskID++
	}

	if strings.Contains(goal, "wait") {
		tasks = append(tasks, Task{
			ID:          fmt.Sprintf("task_%d", taskID),
			Name:        "Wait",
			Description: "Wait for condition or time",
			Type:        "action",
			Action:      "wait",
			Status:      "pending",
			Parameters:  map[string]any{"action_type": "wait"},
		})
		taskID++
	}

	if strings.Contains(goal, "screenshot") {
		tasks = append(tasks, Task{
			ID:          fmt.Sprintf("task_%d", taskID),
			Name:        "Take Screenshot",
			Description: "Capture screen",
			Type:        "action",
			Action:      "screenshot",
			Status:      "pending",
			Parameters:  map[string]any{"action_type": "screenshot"},
		})
		taskID++
	}

	// If no specific actions found, create a generic task
	if len(tasks) == 0 {
		tasks = append(tasks, Task{
			ID:          fmt.Sprintf("task_%d", taskID),
			Name:        "Execute Goal",
			Description: goal,
			Type:        "action",
			Action:      "generic",
			Status:      "pending",
			Parameters:  map[string]any{"goal": goal},
		})
	}

	return tasks
}

// executorNode creates a node that executes tasks from a plan
func executorNode() BaseNode {
	return NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			// Get plan from context
			plan, ok := ctx.Value("plan").(*Plan)
			if !ok {
				return nil, fmt.Errorf("no plan found in context")
			}

			// Get specific task to execute (if specified)
			taskID, _ := ctx.Value("task_id").(string)
			if taskID == "" {
				if taskIDParam, exists := params["task_id"]; exists {
					taskID, _ = taskIDParam.(string)
				}
			}

			return map[string]any{
				"plan":    plan,
				"task_id": taskID,
			}, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			execContext := prepResult.(map[string]any)
			plan := execContext["plan"].(*Plan)
			taskID, _ := execContext["task_id"].(string)

			// Get action executor function from params
			actionFunc, exists := params["action_func"]
			if !exists {
				return nil, fmt.Errorf("no action_func provided in params")
			}

			execFunc, ok := actionFunc.(func(*WorkContext, string, map[string]any) (any, error))
			if !ok {
				return nil, fmt.Errorf("invalid action_func type")
			}

			if taskID != "" {
				// Execute specific task
				task := findTaskByID(plan, taskID)
				if task == nil {
					return nil, fmt.Errorf("task not found: %s", taskID)
				}

				result, err := executeTask(ctx, task, execFunc)
				return map[string]any{
					"task_id": taskID,
					"result":  result,
					"error":   err,
				}, nil
			} else {
				// Execute all ready tasks
				results := make(map[string]any)
				var lastError error

				// Update plan status
				if plan.Status == "created" {
					plan.Status = "running"
					now := time.Now()
					plan.StartedAt = &now
				}

				for i := range plan.Tasks {
					task := &plan.Tasks[i]
					if isTaskReady(plan, task) {
						result, err := executeTask(ctx, task, execFunc)
						results[task.ID] = map[string]any{
							"result": result,
							"error":  err,
						}
						if err != nil {
							lastError = err
						}
					}
				}

				return map[string]any{
					"results": results,
					"error":   lastError,
				}, nil
			}
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			result := execResult.(map[string]any)

			if err, exists := result["error"]; exists && err != nil {
				log.Printf("Task execution failed: %v", err)
				return "failed", nil
			}

			// Update plan status in context
			execContext := prepResult.(map[string]any)
			plan := execContext["plan"].(*Plan)

			// Check if all tasks are completed
			allCompleted := true
			for _, task := range plan.Tasks {
				if task.Status != "completed" {
					allCompleted = false
					break
				}
			}

			if allCompleted {
				plan.Status = "completed"
				now := time.Now()
				plan.CompletedAt = &now
				ctx.SetValue("plan", plan)
				log.Printf("Plan completed: %s", plan.Name)
				return "completed", nil
			}

			return "continue", nil
		})
}

// Helper functions for task execution

// findTaskByID finds a task by its ID in the plan
func findTaskByID(plan *Plan, taskID string) *Task {
	for i := range plan.Tasks {
		if plan.Tasks[i].ID == taskID {
			return &plan.Tasks[i]
		}
		// Search in subtasks recursively
		if found := findTaskInSubTasks(plan.Tasks[i].SubTasks, taskID); found != nil {
			return found
		}
	}
	return nil
}

// findTaskInSubTasks recursively searches for a task in subtasks
func findTaskInSubTasks(subTasks []Task, taskID string) *Task {
	for i := range subTasks {
		if subTasks[i].ID == taskID {
			return &subTasks[i]
		}
		if found := findTaskInSubTasks(subTasks[i].SubTasks, taskID); found != nil {
			return found
		}
	}
	return nil
}

// executeTask executes a single task
func executeTask(ctx *WorkContext, task *Task, actionFunc func(*WorkContext, string, map[string]any) (any, error)) (any, error) {
	// Update task status
	now := time.Now()
	task.Status = "running"
	task.StartTime = &now

	log.Printf("Executing task: %s (%s)", task.Name, task.Type)

	// Execute the task using the provided action function
	result, err := actionFunc(ctx, task.Action, task.Parameters)

	// Update task status and result
	endTime := time.Now()
	task.EndTime = &endTime

	if err != nil {
		task.Status = "failed"
		task.Error = err.Error()
	} else {
		task.Status = "completed"
		task.Result = result
	}

	return result, err
}

// isTaskReady checks if a task is ready to execute (all dependencies completed)
func isTaskReady(plan *Plan, task *Task) bool {
	if task.Status != "pending" {
		return false
	}

	// Check if all dependencies are completed
	for _, depID := range task.Dependencies {
		depTask := findTaskByID(plan, depID)
		if depTask == nil || depTask.Status != "completed" {
			return false
		}
	}

	return true
}

// taskPlannerAgentNode creates a combined planning and execution node
func taskPlannerAgentNode() BaseNode {
	return NewNode().
		SetPrep(func(ctx *WorkContext, params map[string]any) (any, error) {
			// Get execution mode from params
			mode, _ := params["mode"].(string)
			if mode == "" {
				mode = "plan_and_execute"
			}

			return map[string]any{
				"mode": mode,
			}, nil
		}).
		SetExec(func(ctx *WorkContext, params map[string]any, prepResult any) (any, error) {
			context := prepResult.(map[string]any)
			mode := context["mode"].(string)

			var planResult, execResult any

			// Planning phase
			if mode == "plan_only" || mode == "plan_and_execute" {
				// Create a planner node and execute it
				planner := plannerNode()
				planner.SetParams(params) // Pass through parameters

				planAction, err := planner.Run(ctx)
				if err != nil {
					return nil, fmt.Errorf("planning failed: %w", err)
				}

				planResult = map[string]any{
					"action": planAction,
					"plan":   ctx.Value("plan"),
				}
			}

			// Execution phase
			if mode == "execute_only" || mode == "plan_and_execute" {
				// Create an executor node and execute it
				executor := executorNode()
				executor.SetParams(params) // Pass through parameters

				execAction, err := executor.Run(ctx)
				if err != nil {
					return nil, fmt.Errorf("execution failed: %w", err)
				}

				execResult = map[string]any{
					"action": execAction,
					"plan":   ctx.Value("plan"),
				}
			}

			return map[string]any{
				"plan_result": planResult,
				"exec_result": execResult,
				"mode":        mode,
			}, nil
		}).
		SetPost(func(ctx *WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
			result := execResult.(map[string]any)
			mode := result["mode"].(string)

			// Determine final action based on mode and results
			if mode == "plan_only" {
				if planResult, exists := result["plan_result"]; exists && planResult != nil {
					return "plan_completed", nil
				}
				return "plan_failed", nil
			}

			if mode == "execute_only" {
				if execResult, exists := result["exec_result"]; exists && execResult != nil {
					execMap := execResult.(map[string]any)
					if action, exists := execMap["action"]; exists {
						return action.(string), nil
					}
				}
				return "execution_failed", nil
			}

			// plan_and_execute mode
			if execResult, exists := result["exec_result"]; exists && execResult != nil {
				execMap := execResult.(map[string]any)
				if action, exists := execMap["action"]; exists {
					return action.(string), nil
				}
			}

			return "completed", nil
		})
}
