# RPA Agent Workflow System

这是一个基于Go的RPA（机器人流程自动化）Agent系统，能够将高级目标分解为可执行的任务并自动执行。

## 特性

- **任务规划**: 自动将高级目标分解为具体的可执行任务
- **任务执行**: 支持多种类型的RPA操作（点击、输入、等待、截图等）
- **工作流编排**: 支持复杂的任务依赖关系和条件分支
- **函数式风格**: 遵循现有代码库的链式调用模式
- **可扩展性**: 易于添加新的任务类型和执行器

## 核心组件

### 1. 数据结构

```go
// Task 表示单个任务
type Task struct {
    ID           string         `json:"id"`
    Name         string         `json:"name"`
    Description  string         `json:"description"`
    Type         string         `json:"type"` // "action", "condition", "parallel", "sequential"
    Action       string         `json:"action,omitempty"`
    Parameters   map[string]any `json:"parameters,omitempty"`
    Dependencies []string       `json:"dependencies,omitempty"`
    SubTasks     []Task         `json:"sub_tasks,omitempty"`
    Status       string         `json:"status"` // "pending", "running", "completed", "failed"
    Result       any            `json:"result,omitempty"`
    Error        string         `json:"error,omitempty"`
    StartTime    *time.Time     `json:"start_time,omitempty"`
    EndTime      *time.Time     `json:"end_time,omitempty"`
}

// Plan 表示完整的执行计划
type Plan struct {
    ID          string         `json:"id"`
    Name        string         `json:"name"`
    Description string         `json:"description"`
    Tasks       []Task         `json:"tasks"`
    Context     map[string]any `json:"context"`
    Status      string         `json:"status"` // "created", "running", "completed", "failed"
    CreatedAt   time.Time      `json:"created_at"`
    StartedAt   *time.Time     `json:"started_at,omitempty"`
    CompletedAt *time.Time     `json:"completed_at,omitempty"`
}
```

### 2. 节点工厂函数

#### plannerNode()
创建一个规划节点，负责将高级目标分解为具体任务：

```go
planner := plannerNode()
planner.SetParams(map[string]any{
    "goal": "click button and type text",
    "plan_func": customPlanFunc, // 可选的自定义规划函数
})
```

#### executorNode()
创建一个执行节点，负责执行计划中的任务：

```go
executor := executorNode()
executor.SetParams(map[string]any{
    "action_func": rpaActionFunc, // RPA动作执行函数
})
```

#### taskPlannerAgentNode()
创建一个组合节点，同时包含规划和执行功能：

```go
agent := taskPlannerAgentNode()
agent.SetParams(map[string]any{
    "mode": "plan_and_execute", // "plan_only", "execute_only", "plan_and_execute"
    "goal": "automate login process",
    "action_func": rpaActionFunc,
})
```

## 使用示例

### 基本用法

```go
package main

import (
    "context"
    "log"
    "rpa_server/pkg/workflow"
)

func main() {
    // 创建RPA动作执行函数
    rpaActionFunc := func(ctx *workflow.WorkContext, action string, params map[string]any) (any, error) {
        switch action {
        case "click":
            x, _ := params["x"].(float64)
            y, _ := params["y"].(float64)
            log.Printf("Clicking at (%.0f, %.0f)", x, y)
            return map[string]any{"success": true}, nil
        case "type_text":
            text, _ := params["text"].(string)
            log.Printf("Typing: %s", text)
            return map[string]any{"success": true}, nil
        default:
            return nil, fmt.Errorf("unknown action: %s", action)
        }
    }
    
    // 创建工作流
    planner := workflow.plannerNode()
    executor := workflow.executorNode()
    executor.SetParams(map[string]any{
        "action_func": rpaActionFunc,
    })
    
    // 连接节点
    planner.Next("plan_ready", executor)
    
    // 创建流程并运行
    flow := workflow.NewFlow(planner)
    ctx := workflow.WithParam(context.Background(), nil)
    ctx.SetValue("goal", "click button and type hello world")
    
    finalAction, err := flow.Run(ctx)
    if err != nil {
        log.Printf("Error: %v", err)
    } else {
        log.Printf("Completed with action: %s", finalAction)
    }
}
```

### 自定义规划逻辑

```go
// 自定义规划函数
customPlanFunc := func(goal string, context map[string]any) (*workflow.Plan, error) {
    plan := &workflow.Plan{
        ID:   "custom_plan",
        Name: fmt.Sprintf("Custom Plan for: %s", goal),
    }
    
    if goal == "login to website" {
        plan.Tasks = []workflow.Task{
            {
                ID:         "task_1",
                Name:       "Navigate to login",
                Type:       "action",
                Action:     "click",
                Status:     "pending",
                Parameters: map[string]any{"x": 200, "y": 100},
            },
            {
                ID:          "task_2",
                Name:        "Enter username",
                Type:        "action",
                Action:      "type_text",
                Status:      "pending",
                Parameters:  map[string]any{"text": "<EMAIL>"},
                Dependencies: []string{"task_1"},
            },
            // 更多任务...
        }
    }
    
    return plan, nil
}

// 使用自定义规划函数
planner := workflow.plannerNode()
planner.SetParams(map[string]any{
    "plan_func": customPlanFunc,
})
```

### 复杂工作流

```go
// 创建分析节点
analyzer := workflow.NewNode().
    SetExec(func(ctx *workflow.WorkContext, params map[string]any, prepResult any) (any, error) {
        goal := ctx.Value("goal").(string)
        complexity := "simple"
        if len(goal) > 50 {
            complexity = "complex"
        }
        ctx.SetValue("complexity", complexity)
        return complexity, nil
    }).
    SetPost(func(ctx *workflow.WorkContext, params map[string]any, prepResult any, execResult any) (string, error) {
        complexity := execResult.(string)
        if complexity == "complex" {
            return "complex_planning", nil
        }
        return "simple_planning", nil
    })

// 创建不同的规划器
simplePlanner := workflow.plannerNode()
complexPlanner := workflow.plannerNode()
executor := workflow.executorNode()

// 连接工作流
analyzer.Next("simple_planning", simplePlanner).Next("plan_ready", executor)
analyzer.Next("complex_planning", complexPlanner).Next("plan_ready", executor)

// 运行工作流
flow := workflow.NewFlow(analyzer)
// ... 设置上下文和运行
```

## 测试

运行测试：

```bash
go test ./pkg/workflow -v
```

运行演示：

```bash
go run cmd/agent_demo/main.go
```

## 扩展

### 添加新的任务类型

1. 在`parseGoalIntoTasks`函数中添加新的关键词识别
2. 在RPA动作函数中添加新的动作处理
3. 可选：创建专门的执行器类型

### 集成现有RPA服务

将agent系统与现有的RPA服务集成：

```go
// 集成现有的RPA服务
rpaActionFunc := func(ctx *workflow.WorkContext, action string, params map[string]any) (any, error) {
    // 调用现有的RPA服务API
    return rpaServer.ExecuteAction(action, params)
}
```

## 架构优势

1. **模块化设计**: 规划和执行分离，易于维护和扩展
2. **函数式风格**: 遵循现有代码库的设计模式
3. **类型安全**: 使用Go的强类型系统确保代码质量
4. **可测试性**: 每个组件都可以独立测试
5. **可观测性**: 内置日志和状态跟踪

这个agent系统为RPA自动化提供了一个强大而灵活的基础框架。
