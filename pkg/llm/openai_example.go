package llm

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
)

// ExampleOpenAIUsage demonstrates how to use the OpenAI client
func ExampleOpenAIUsage() {
	// Check if API key is available
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		log.Println("OPENAI_API_KEY not set, using mock client for demonstration")
		ExampleMockUsage()
		return
	}

	// Create OpenAI client
	client := NewOpenAIClientV2(apiKey)
	client.SetModel("gpt-3.5-turbo").SetTimeout(30 * time.Second)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fmt.Println("=== OpenAI Client Demo ===")

	// Example 1: Basic Chat
	fmt.Println("\n1. Basic Chat Example:")
	chatExample(client, ctx)

	// Example 2: Task Planning
	fmt.Println("\n2. Task Planning Example:")
	planningExample(client, ctx)

	// Example 3: Goal Analysis
	fmt.Println("\n3. Goal Analysis Example:")
	analysisExample(client, ctx)

	// Example 4: Code Generation
	fmt.Println("\n4. Code Generation Example:")
	codeGenerationExample(client, ctx)
}

func chatExample(client *OpenAIClientV2, ctx context.Context) {
	messages := []Message{
		{Role: "system", Content: "You are a helpful RPA assistant."},
		{Role: "user", Content: "Explain what RPA is in one sentence."},
	}

	response, err := client.Chat(ctx, messages)
	if err != nil {
		log.Printf("Chat error: %v", err)
		return
	}

	if response.Error != "" {
		log.Printf("Chat API error: %s", response.Error)
		return
	}

	fmt.Printf("Response: %s\n", response.Content)
	if response.Usage != nil {
		fmt.Printf("Tokens used: %d\n", response.Usage.TotalTokens)
	}
}

func planningExample(client *OpenAIClientV2, ctx context.Context) {
	goal := "automate invoice processing workflow"
	context := map[string]any{
		"system": "accounting software",
		"format": "PDF invoices",
	}

	response, err := client.PlanTasks(ctx, goal, context)
	if err != nil {
		log.Printf("Planning error: %v", err)
		return
	}

	fmt.Printf("Plan: %s\n", response.Description)
	fmt.Printf("Complexity: %s\n", response.Complexity)
	fmt.Printf("Tasks (%d):\n", len(response.Tasks))

	for i, task := range response.Tasks {
		fmt.Printf("  %d. %s (%s)\n", i+1, task.Name, task.Action)
		if len(task.Dependencies) > 0 {
			fmt.Printf("     Dependencies: %v\n", task.Dependencies)
		}
	}
}

func analysisExample(client *OpenAIClientV2, ctx context.Context) {
	goal := "automate customer onboarding process with document verification"

	response, err := client.AnalyzeGoal(ctx, goal)
	if err != nil {
		log.Printf("Analysis error: %v", err)
		return
	}

	fmt.Printf("Complexity: %s\n", response.Complexity)
	fmt.Printf("Feasibility: %s\n", response.Feasibility)
	fmt.Printf("Keywords: %v\n", response.Keywords)
	fmt.Printf("Required Actions: %v\n", response.Actions)
	fmt.Printf("Suggestions:\n")
	for i, suggestion := range response.Suggestions {
		fmt.Printf("  %d. %s\n", i+1, suggestion)
	}
}

func codeGenerationExample(client *OpenAIClientV2, ctx context.Context) {
	prompt := "Create a Go function that validates a credit card number using the Luhn algorithm"
	language := "go"

	response, err := client.GenerateCode(ctx, prompt, language)
	if err != nil {
		log.Printf("Code generation error: %v", err)
		return
	}

	fmt.Printf("Generated %s code:\n", response.Language)
	fmt.Printf("```%s\n%s\n```\n", response.Language, response.Code)
	fmt.Printf("Explanation: %s\n", response.Explanation)
}

// ExampleMockUsage demonstrates the mock client
func ExampleMockUsage() {
	fmt.Println("=== Mock Client Demo ===")

	client := NewMockLLMClient()
	ctx := context.Background()

	// Example 1: Task Planning with Mock
	fmt.Println("\n1. Mock Task Planning:")
	goal := "automate email marketing campaign"

	response, err := client.PlanTasks(ctx, goal, nil)
	if err != nil {
		log.Printf("Mock planning error: %v", err)
		return
	}

	fmt.Printf("Plan: %s\n", response.Description)
	fmt.Printf("Tasks (%d):\n", len(response.Tasks))
	for i, task := range response.Tasks {
		fmt.Printf("  %d. %s (%s)\n", i+1, task.Name, task.Action)
	}

	// Example 2: Goal Analysis with Mock
	fmt.Println("\n2. Mock Goal Analysis:")
	analysis, err := client.AnalyzeGoal(ctx, goal)
	if err != nil {
		log.Printf("Mock analysis error: %v", err)
		return
	}

	fmt.Printf("Complexity: %s\n", analysis.Complexity)
	fmt.Printf("Keywords: %v\n", analysis.Keywords)
	fmt.Printf("Actions: %v\n", analysis.Actions)
}

// ExampleFactoryUsage demonstrates using the factory pattern
func ExampleFactoryUsage() {
	fmt.Println("=== Factory Pattern Demo ===")

	// Example 1: Auto-detect from environment
	fmt.Println("\n1. Auto-detection from environment:")
	_ = NewClientFromEnv() // Create client but don't use it directly

	// Test with a simple chat
	response, err := QuickChat("Hello, what can you help me with?")
	if err != nil {
		log.Printf("Quick chat error: %v", err)
	} else {
		fmt.Printf("Response: %s\n", response.Content)
	}

	// Example 2: Manual configuration
	fmt.Println("\n2. Manual configuration:")
	config := &Config{
		Type:  ClientTypeMock, // Use mock for demo
		Model: "gpt-4",
	}

	configuredClient := NewClient(config)

	// Test planning
	plan, err := configuredClient.PlanTasks(context.Background(), "organize team meeting", nil)
	if err != nil {
		log.Printf("Planning error: %v", err)
	} else {
		fmt.Printf("Plan complexity: %s\n", plan.Complexity)
		fmt.Printf("Number of tasks: %d\n", len(plan.Tasks))
	}

	// Example 3: Quick functions
	fmt.Println("\n3. Quick functions:")

	// Quick planning
	quickPlan, err := QuickPlan("backup database")
	if err != nil {
		log.Printf("Quick plan error: %v", err)
	} else {
		fmt.Printf("Quick plan has %d tasks\n", len(quickPlan.Tasks))
	}

	// Quick analysis
	quickAnalysis, err := QuickAnalyze("complex data migration")
	if err != nil {
		log.Printf("Quick analysis error: %v", err)
	} else {
		fmt.Printf("Quick analysis complexity: %s\n", quickAnalysis.Complexity)
	}
}

// Simple types for demonstration (normally these would be imported from workflow package)
type Plan struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Tasks       []Task         `json:"tasks"`
	Status      string         `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	Context     map[string]any `json:"context"`
}

type Task struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Type         string         `json:"type"`
	Action       string         `json:"action"`
	Parameters   map[string]any `json:"parameters"`
	Dependencies []string       `json:"dependencies"`
	Status       string         `json:"status"`
}

// ExampleIntegrationWithWorkflow shows how to integrate with the workflow package
func ExampleIntegrationWithWorkflow() {
	fmt.Println("=== Workflow Integration Demo ===")

	// Create LLM client
	llmClient := NewClientFromEnv()

	// Example: Create a planning function for workflow integration
	planningFunc := func(goal string, contextData map[string]any) (*Plan, error) {
		// Use LLM to plan tasks
		llmResponse, err := llmClient.PlanTasks(context.Background(), goal, contextData)
		if err != nil {
			return nil, err
		}

		// Convert LLM response to workflow Plan format
		plan := &Plan{
			ID:          fmt.Sprintf("llm_plan_%d", time.Now().Unix()),
			Name:        fmt.Sprintf("LLM Plan: %s", goal),
			Description: llmResponse.Description,
			Status:      "created",
			CreatedAt:   time.Now(),
			Context:     contextData,
		}

		// Convert LLM tasks to workflow tasks
		for _, llmTask := range llmResponse.Tasks {
			task := Task{
				ID:           llmTask.ID,
				Name:         llmTask.Name,
				Description:  llmTask.Description,
				Type:         llmTask.Type,
				Action:       llmTask.Action,
				Parameters:   llmTask.Parameters,
				Dependencies: llmTask.Dependencies,
				Status:       "pending",
			}
			plan.Tasks = append(plan.Tasks, task)
		}

		return plan, nil
	}

	// Test the planning function
	testPlan, err := planningFunc("automate report generation", map[string]any{
		"format":   "PDF",
		"schedule": "weekly",
	})

	if err != nil {
		log.Printf("Integration planning error: %v", err)
	} else {
		fmt.Printf("Integration plan created: %s\n", testPlan.Name)
		fmt.Printf("Tasks: %d\n", len(testPlan.Tasks))
		for _, task := range testPlan.Tasks {
			fmt.Printf("  - %s (%s)\n", task.Name, task.Action)
		}
	}
}

// RunAllExamples runs all the examples
func RunAllExamples() {
	fmt.Println("🚀 Running LLM Client Examples...")

	ExampleOpenAIUsage()
	fmt.Println("\n" + strings.Repeat("=", 50))

	ExampleFactoryUsage()
	fmt.Println("\n" + strings.Repeat("=", 50))

	ExampleIntegrationWithWorkflow()

	fmt.Println("\n✅ All examples completed!")
}
