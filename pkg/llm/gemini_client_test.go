package llm

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGeminiClient(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		t.Skip("GEMINI_API_KEY not set, skipping Gemini client tests")
	}

	client := NewGeminiClient(apiKey)
	client.SetModel("gemini-1.5-flash").SetTimeout(30 * time.Second)

	t.Run("Chat", func(t *testing.T) {
		messages := []Message{
			{Role: "system", Content: "You are a helpful assistant."},
			{Role: "user", Content: "Say hello in a friendly way."},
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.Chat(ctx, messages)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
		assert.Empty(t, response.Error)
		
		t.Logf("Response: %s", response.Content)
		if response.Usage != nil {
			t.Logf("Usage: %+v", response.Usage)
		}
	})

	t.Run("PlanTasks", func(t *testing.T) {
		goal := "login to a website"
		
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.PlanTasks(ctx, goal, nil)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Tasks)
		assert.NotEmpty(t, response.Description)
		assert.Contains(t, []string{"simple", "medium", "complex"}, response.Complexity)
		
		t.Logf("Plan: %s", response.Description)
		t.Logf("Complexity: %s", response.Complexity)
		t.Logf("Tasks: %d", len(response.Tasks))
		
		for i, task := range response.Tasks {
			t.Logf("Task %d: %s - %s", i+1, task.Name, task.Action)
		}
	})

	t.Run("AnalyzeGoal", func(t *testing.T) {
		goal := "automate data entry into a complex form"
		
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.AnalyzeGoal(ctx, goal)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Keywords)
		assert.NotEmpty(t, response.Actions)
		assert.Contains(t, []string{"simple", "medium", "complex"}, response.Complexity)
		assert.Contains(t, []string{"high", "medium", "low"}, response.Feasibility)
		
		t.Logf("Complexity: %s", response.Complexity)
		t.Logf("Feasibility: %s", response.Feasibility)
		t.Logf("Keywords: %v", response.Keywords)
		t.Logf("Actions: %v", response.Actions)
		t.Logf("Suggestions: %v", response.Suggestions)
	})

	t.Run("GenerateCode", func(t *testing.T) {
		prompt := "Create a function that validates an email address"
		language := "go"
		
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.GenerateCode(ctx, prompt, language)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Code)
		assert.Equal(t, language, response.Language)
		assert.NotEmpty(t, response.Explanation)
		
		t.Logf("Generated code:\n%s", response.Code)
		t.Logf("Explanation: %s", response.Explanation)
	})
}

func TestGeminiClientConfiguration(t *testing.T) {
	client := NewGeminiClient("test-key")
	
	// Test default values
	assert.Equal(t, "gemini-1.5-flash", client.model)
	assert.Equal(t, 30*time.Second, client.timeout)
	assert.Equal(t, "test-key", client.apiKey)
	
	// Test configuration
	client.SetModel("gemini-1.5-pro").SetTimeout(60 * time.Second)
	assert.Equal(t, "gemini-1.5-pro", client.model)
	assert.Equal(t, 60*time.Second, client.timeout)
}

func TestGeminiClientErrorHandling(t *testing.T) {
	// Test with invalid API key
	client := NewGeminiClient("invalid-key")
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	t.Run("Chat with invalid key", func(t *testing.T) {
		messages := []Message{
			{Role: "user", Content: "Hello"},
		}

		response, err := client.Chat(ctx, messages)
		// Should not return error, but response should contain error message
		require.NoError(t, err)
		assert.NotEmpty(t, response.Error)
		assert.Contains(t, response.Error, "failed to initialize Gemini client")
	})

	t.Run("PlanTasks with invalid key", func(t *testing.T) {
		_, err := client.PlanTasks(ctx, "test goal", nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to get Gemini response")
	})
}

func TestFactoryWithGeminiClient(t *testing.T) {
	t.Run("With API key", func(t *testing.T) {
		config := &Config{
			Type:   ClientTypeGemini,
			APIKey: "test-key",
			Model:  "gemini-1.5-pro",
		}

		client := NewClient(config)
		assert.NotNil(t, client)
		
		// Should be GeminiClient
		geminiClient, ok := client.(*GeminiClient)
		assert.True(t, ok, "Should create GeminiClient")
		assert.Equal(t, "gemini-1.5-pro", geminiClient.model)
		assert.Equal(t, "test-key", geminiClient.apiKey)
	})

	t.Run("Without API key", func(t *testing.T) {
		config := &Config{
			Type:   ClientTypeGemini,
			APIKey: "", // No API key
		}

		client := NewClient(config)
		assert.NotNil(t, client)
		
		// Should fallback to mock
		_, isMock := client.(*MockLLMClient)
		assert.True(t, isMock, "Should fallback to mock when no API key")
	})
}

func TestGeminiFromEnv(t *testing.T) {
	// Test environment variable detection
	originalKey := os.Getenv("GEMINI_API_KEY")
	originalType := os.Getenv("LLM_CLIENT_TYPE")
	
	// Clean up after test
	defer func() {
		if originalKey != "" {
			os.Setenv("GEMINI_API_KEY", originalKey)
		} else {
			os.Unsetenv("GEMINI_API_KEY")
		}
		if originalType != "" {
			os.Setenv("LLM_CLIENT_TYPE", originalType)
		} else {
			os.Unsetenv("LLM_CLIENT_TYPE")
		}
	}()

	t.Run("Auto-detect from GEMINI_API_KEY", func(t *testing.T) {
		os.Setenv("GEMINI_API_KEY", "test-gemini-key")
		os.Unsetenv("LLM_CLIENT_TYPE")
		
		client := NewClientFromEnv()
		geminiClient, ok := client.(*GeminiClient)
		assert.True(t, ok, "Should create GeminiClient when GEMINI_API_KEY is set")
		assert.Equal(t, "test-gemini-key", geminiClient.apiKey)
		assert.Equal(t, "gemini-1.5-flash", geminiClient.model)
	})

	t.Run("Explicit client type", func(t *testing.T) {
		os.Setenv("GEMINI_API_KEY", "test-gemini-key")
		os.Setenv("LLM_CLIENT_TYPE", "gemini")
		
		client := NewClientFromEnv()
		geminiClient, ok := client.(*GeminiClient)
		assert.True(t, ok, "Should create GeminiClient when LLM_CLIENT_TYPE=gemini")
		assert.Equal(t, "test-gemini-key", geminiClient.apiKey)
	})

	t.Run("Custom model", func(t *testing.T) {
		os.Setenv("GEMINI_API_KEY", "test-gemini-key")
		os.Setenv("LLM_MODEL", "gemini-1.5-pro")
		
		client := NewClientFromEnv()
		geminiClient, ok := client.(*GeminiClient)
		assert.True(t, ok, "Should create GeminiClient")
		assert.Equal(t, "gemini-1.5-pro", geminiClient.model)
	})
}

func TestGeminiClientClose(t *testing.T) {
	client := NewGeminiClient("test-key")
	
	// Should not error when client is not initialized
	err := client.Close()
	assert.NoError(t, err)
	
	// Initialize client (this would normally happen during first API call)
	// We can't test actual close without a real API key, but we can test the method exists
	assert.NotNil(t, client.Close)
}

func BenchmarkGeminiClient(b *testing.B) {
	apiKey := os.Getenv("GEMINI_API_KEY")
	if apiKey == "" {
		b.Skip("GEMINI_API_KEY not set, skipping benchmark")
	}

	client := NewGeminiClient(apiKey)
	messages := []Message{
		{Role: "user", Content: "Say hello"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		_, err := client.Chat(ctx, messages)
		cancel()
		if err != nil {
			b.Fatalf("Chat failed: %v", err)
		}
	}
}

func TestGeminiClientComparison(t *testing.T) {
	// Test that Gemini and OpenAI clients have similar interfaces
	geminiClient := NewGeminiClient("test-key")
	openaiClient := NewOpenAIClientV2("test-key")
	
	// Both should implement LLMClient interface
	var _ LLMClient = geminiClient
	var _ LLMClient = openaiClient
	
	// Both should have similar configuration methods
	assert.NotNil(t, geminiClient.SetModel)
	assert.NotNil(t, geminiClient.SetTimeout)
	assert.NotNil(t, openaiClient.SetModel)
	assert.NotNil(t, openaiClient.SetTimeout)
}
