package llm

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/option"
)

// GeminiClient implements LLMClient using Google's Gemini API
type GeminiClient struct {
	client  *genai.Client
	apiKey  string
	model   string
	timeout time.Duration
}

// NewGeminiClient creates a new Gemini client
func NewGeminiClient(apiKey string) *GeminiClient {
	return &GeminiClient{
		apiKey:  apiKey,
		model:   "gemini-1.5-flash",
		timeout: 30 * time.Second,
	}
}

// initClient initializes the Gemini client (lazy initialization)
func (c *GeminiClient) initClient(ctx context.Context) error {
	if c.client != nil {
		return nil
	}

	client, err := genai.NewClient(ctx, option.WithAPIKey(c.apiKey))
	if err != nil {
		return fmt.Errorf("failed to create Gemini client: %w", err)
	}

	c.client = client
	return nil
}

// SetModel sets the model to use
func (c *GeminiClient) SetModel(model string) *GeminiClient {
	c.model = model
	return c
}

// SetTimeout sets the request timeout
func (c *GeminiClient) SetTimeout(timeout time.Duration) *GeminiClient {
	c.timeout = timeout
	return c
}

// Chat implements LLMClient interface using Gemini API
func (c *GeminiClient) Chat(ctx context.Context, messages []Message) (*Response, error) {
	if ctx == nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), c.timeout)
		defer cancel()
	}

	// Initialize client if needed
	if err := c.initClient(ctx); err != nil {
		return &Response{
			Error: fmt.Sprintf("failed to initialize Gemini client: %v", err),
		}, nil
	}

	// Get the model
	model := c.client.GenerativeModel(c.model)

	// Configure model settings
	model.SetTemperature(0.7)
	model.SetMaxOutputTokens(2048)

	// Convert messages to Gemini format
	var parts []genai.Part
	for _, msg := range messages {
		// Gemini doesn't have explicit roles like OpenAI, so we combine all messages
		content := fmt.Sprintf("%s: %s", msg.Role, msg.Content)
		parts = append(parts, genai.Text(content))
	}

	// Generate content
	resp, err := model.GenerateContent(ctx, parts...)
	if err != nil {
		return &Response{
			Error: fmt.Sprintf("Gemini API error: %v", err),
		}, nil
	}

	if len(resp.Candidates) == 0 {
		return &Response{
			Error: "no response candidates returned from Gemini",
		}, nil
	}

	// Extract text from the response
	var responseText strings.Builder
	for _, part := range resp.Candidates[0].Content.Parts {
		if txt, ok := part.(genai.Text); ok {
			responseText.WriteString(string(txt))
		}
	}

	// Convert usage information
	var usage *Usage
	if resp.UsageMetadata != nil {
		usage = &Usage{
			PromptTokens:     int(resp.UsageMetadata.PromptTokenCount),
			CompletionTokens: int(resp.UsageMetadata.CandidatesTokenCount),
			TotalTokens:      int(resp.UsageMetadata.TotalTokenCount),
		}
	}

	return &Response{
		Content: responseText.String(),
		Usage:   usage,
		Metadata: map[string]any{
			"model":         c.model,
			"finish_reason": resp.Candidates[0].FinishReason,
		},
	}, nil
}

// PlanTasks implements task planning using Gemini API
func (c *GeminiClient) PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error) {
	contextStr := ""
	if context != nil {
		contextBytes, _ := json.MarshalIndent(context, "", "  ")
		contextStr = string(contextBytes)
	}

	prompt := fmt.Sprintf(`You are an RPA (Robotic Process Automation) task planner. Given a high-level goal, break it down into specific, executable tasks.

Goal: %s

Context: %s

Available Actions:
- click: Click at coordinates (x, y)
- double_click: Double click at coordinates
- type_text: Type text into input field
- key_press: Press keyboard keys
- wait: Wait for specified duration
- screenshot: Take a screenshot
- scroll: Scroll in a direction
- drag: Drag from one point to another
- move_mouse: Move mouse to coordinates

Please respond with a JSON object containing:
{
  "tasks": [
    {
      "id": "task_1",
      "name": "Task Name",
      "description": "Detailed description",
      "type": "action",
      "action": "click",
      "parameters": {"x": 100, "y": 200},
      "dependencies": [],
      "priority": 1
    }
  ],
  "description": "Overall plan description",
  "complexity": "simple|medium|complex"
}

Make sure tasks are in logical order with proper dependencies.`, goal, contextStr)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA task planner. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get Gemini response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("Gemini error: %s", response.Error)
	}

	var planResp PlanResponse
	if err := json.Unmarshal([]byte(response.Content), &planResp); err != nil {
		// If JSON parsing fails, try to extract JSON from the response
		jsonContent := extractJSONFromResponse(response.Content)
		if jsonContent != "" {
			if err2 := json.Unmarshal([]byte(jsonContent), &planResp); err2 == nil {
				// Successfully parsed extracted JSON
			} else {
				return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w (original error: %v)", err2, err)
			}
		} else {
			return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w", err)
		}
	}

	planResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &planResp, nil
}

// AnalyzeGoal implements goal analysis using Gemini API
func (c *GeminiClient) AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error) {
	prompt := fmt.Sprintf(`Analyze the following RPA goal and provide insights:

Goal: %s

Please respond with a JSON object containing:
{
  "complexity": "simple|medium|complex",
  "keywords": ["keyword1", "keyword2"],
  "actions": ["action1", "action2"],
  "feasibility": "high|medium|low",
  "suggestions": ["suggestion1", "suggestion2"]
}

Consider:
- How many steps are involved
- What RPA actions are needed
- Potential challenges
- Alternative approaches`, goal)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA analyst. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get Gemini response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("Gemini error: %s", response.Error)
	}

	var analysisResp AnalysisResponse
	if err := json.Unmarshal([]byte(response.Content), &analysisResp); err != nil {
		// Try to extract JSON from response
		jsonContent := extractJSONFromResponse(response.Content)
		if jsonContent != "" {
			if err2 := json.Unmarshal([]byte(jsonContent), &analysisResp); err2 == nil {
				// Successfully parsed extracted JSON
			} else {
				return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w (original error: %v)", err2, err)
			}
		} else {
			return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w", err)
		}
	}

	analysisResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &analysisResp, nil
}

// GenerateCode implements code generation using Gemini API
func (c *GeminiClient) GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error) {
	fullPrompt := fmt.Sprintf(`Generate %s code for the following requirement:

%s

Please respond with a JSON object containing:
{
  "code": "// Generated code here",
  "language": "%s",
  "explanation": "Explanation of the code"
}

Make sure the code is syntactically correct and follows best practices.`, language, prompt, language)

	messages := []Message{
		{Role: "system", Content: "You are an expert programmer. Always respond with valid JSON containing executable code."},
		{Role: "user", Content: fullPrompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get Gemini response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("Gemini error: %s", response.Error)
	}

	var codeResp CodeResponse
	if err := json.Unmarshal([]byte(response.Content), &codeResp); err != nil {
		// If JSON parsing fails, try to extract code from markdown
		code := extractCodeFromMarkdown(response.Content, language)
		if code != "" {
			return &CodeResponse{
				Code:        code,
				Language:    language,
				Explanation: "Code extracted from Gemini response",
				Metadata: map[string]any{
					"usage":     response.Usage,
					"model":     c.model,
					"extracted": true,
				},
			}, nil
		}

		// Try to extract JSON from response
		jsonContent := extractJSONFromResponse(response.Content)
		if jsonContent != "" {
			if err2 := json.Unmarshal([]byte(jsonContent), &codeResp); err2 == nil {
				// Successfully parsed extracted JSON
			} else {
				return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w (original error: %v)", err2, err)
			}
		} else {
			return nil, fmt.Errorf("failed to parse Gemini response as JSON: %w", err)
		}
	}

	codeResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &codeResp, nil
}

// Close closes the Gemini client
func (c *GeminiClient) Close() error {
	if c.client != nil {
		return c.client.Close()
	}
	return nil
}
