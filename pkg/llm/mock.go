package llm

import (
	"context"
	"fmt"
	"strings"
	"time"
)

// <PERSON>ckLL<PERSON>lient implements LLMClient for testing and development
type MockLLMClient struct {
	Delay time.Duration // Simulate API delay
}

// NewMockLLMClient creates a new mock LLM client
func NewMockLLMClient() *MockLLMClient {
	return &MockLLMClient{
		Delay: 100 * time.Millisecond, // Default delay
	}
}

// SetDelay sets the simulated API delay
func (m *MockLLMClient) SetDelay(delay time.Duration) *MockLLMClient {
	m.Delay = delay
	return m
}

// Cha<PERSON> implements LLMClient interface with mock responses
func (m *MockLLMClient) Chat(ctx context.Context, messages []Message) (*Response, error) {
	if m.Delay > 0 {
		time.Sleep(m.Delay)
	}

	// Simple mock response based on the last user message
	var lastUserMessage string
	for _, msg := range messages {
		if msg.Role == "user" {
			lastUserMessage = msg.Content
		}
	}

	response := fmt.Sprintf("Mock response to: %s", lastUserMessage)

	return &Response{
		Content: response,
		Usage: &Usage{
			PromptTokens:     len(lastUserMessage) / 4, // Rough token estimation
			CompletionTokens: len(response) / 4,
			TotalTokens:      (len(lastUserMessage) + len(response)) / 4,
		},
		Metadata: map[string]any{
			"mock":  true,
			"model": "mock-gpt",
		},
	}, nil
}

// PlanTasks implements task planning with mock logic
func (m *MockLLMClient) PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error) {
	if m.Delay > 0 {
		time.Sleep(m.Delay)
	}

	goal = strings.ToLower(goal)
	var tasks []TaskPlan
	taskID := 1

	// Mock planning logic based on keywords
	if strings.Contains(goal, "login") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Navigate to login page",
			Description:  "Click on the login button or link",
			Type:         "action",
			Action:       "click",
			Parameters:   map[string]any{"x": 200, "y": 100, "element": "login_button"},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++

		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Enter username",
			Description:  "Type username in the username field",
			Type:         "action",
			Action:       "type_text",
			Parameters:   map[string]any{"text": "<EMAIL>", "field": "username"},
			Dependencies: []string{"task_1"},
			Priority:     2,
		})
		taskID++

		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Enter password",
			Description:  "Type password in the password field",
			Type:         "action",
			Action:       "type_text",
			Parameters:   map[string]any{"text": "password123", "field": "password"},
			Dependencies: []string{"task_2"},
			Priority:     3,
		})
		taskID++

		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Submit login",
			Description:  "Click the submit button",
			Type:         "action",
			Action:       "click",
			Parameters:   map[string]any{"x": 300, "y": 400, "element": "submit_button"},
			Dependencies: []string{"task_3"},
			Priority:     4,
		})
		taskID++
	}

	if strings.Contains(goal, "click") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Click Action",
			Description:  "Perform click action",
			Type:         "action",
			Action:       "click",
			Parameters:   map[string]any{"x": 100, "y": 200},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++
	}

	if strings.Contains(goal, "type") || strings.Contains(goal, "input") || strings.Contains(goal, "text") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Type Text",
			Description:  "Type text input",
			Type:         "action",
			Action:       "type_text",
			Parameters:   map[string]any{"text": "Hello World"},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++
	}

	if strings.Contains(goal, "wait") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Wait",
			Description:  "Wait for specified duration",
			Type:         "action",
			Action:       "wait",
			Parameters:   map[string]any{"duration": 2000},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++
	}

	if strings.Contains(goal, "screenshot") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Take Screenshot",
			Description:  "Capture screen",
			Type:         "action",
			Action:       "screenshot",
			Parameters:   map[string]any{"filename": "screenshot.png"},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++
	}

	if strings.Contains(goal, "form") {
		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Fill Form Field 1",
			Description:  "Fill the first form field",
			Type:         "action",
			Action:       "type_text",
			Parameters:   map[string]any{"text": "John Doe", "field": "name"},
			Dependencies: []string{},
			Priority:     1,
		})
		taskID++

		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Fill Form Field 2",
			Description:  "Fill the second form field",
			Type:         "action",
			Action:       "type_text",
			Parameters:   map[string]any{"text": "<EMAIL>", "field": "email"},
			Dependencies: []string{fmt.Sprintf("task_%d", taskID-1)},
			Priority:     2,
		})
		taskID++

		tasks = append(tasks, TaskPlan{
			ID:           fmt.Sprintf("task_%d", taskID),
			Name:         "Submit Form",
			Description:  "Submit the form",
			Type:         "action",
			Action:       "click",
			Parameters:   map[string]any{"x": 250, "y": 350, "element": "submit"},
			Dependencies: []string{fmt.Sprintf("task_%d", taskID-1)},
			Priority:     3,
		})
		taskID++
	}

	// If no specific patterns found, create a generic task
	if len(tasks) == 0 {
		tasks = append(tasks, TaskPlan{
			ID:           "task_1",
			Name:         "Execute Goal",
			Description:  goal,
			Type:         "action",
			Action:       "generic",
			Parameters:   map[string]any{"goal": goal},
			Dependencies: []string{},
			Priority:     1,
		})
	}

	complexity := "simple"
	if len(tasks) > 3 {
		complexity = "medium"
	}
	if len(tasks) > 6 {
		complexity = "complex"
	}

	return &PlanResponse{
		Tasks:       tasks,
		Description: fmt.Sprintf("Mock plan for: %s", goal),
		Complexity:  complexity,
		Metadata: map[string]any{
			"mock":       true,
			"model":      "mock-gpt",
			"task_count": len(tasks),
		},
	}, nil
}

// AnalyzeGoal implements goal analysis with mock logic
func (m *MockLLMClient) AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error) {
	if m.Delay > 0 {
		time.Sleep(m.Delay)
	}

	goal = strings.ToLower(goal)

	// Extract keywords
	keywords := []string{}
	actions := []string{}

	if strings.Contains(goal, "click") {
		keywords = append(keywords, "click")
		actions = append(actions, "click")
	}
	if strings.Contains(goal, "type") || strings.Contains(goal, "input") {
		keywords = append(keywords, "type", "input")
		actions = append(actions, "type_text")
	}
	if strings.Contains(goal, "login") {
		keywords = append(keywords, "login", "authentication")
		actions = append(actions, "click", "type_text")
	}
	if strings.Contains(goal, "form") {
		keywords = append(keywords, "form", "submit")
		actions = append(actions, "type_text", "click")
	}
	if strings.Contains(goal, "wait") {
		keywords = append(keywords, "wait", "delay")
		actions = append(actions, "wait")
	}
	if strings.Contains(goal, "screenshot") {
		keywords = append(keywords, "screenshot", "capture")
		actions = append(actions, "screenshot")
	}

	// Add default keywords if none found
	if len(keywords) == 0 {
		keywords = append(keywords, "automation", "task")
	}

	// Determine complexity
	complexity := "simple"
	if len(goal) > 30 || len(actions) > 2 {
		complexity = "medium"
	}
	if len(goal) > 80 || len(actions) > 5 || strings.Contains(goal, "complex") {
		complexity = "complex"
	}

	// Determine feasibility
	feasibility := "high"
	if strings.Contains(goal, "complex") || strings.Contains(goal, "difficult") {
		feasibility = "medium"
	}
	if strings.Contains(goal, "impossible") || strings.Contains(goal, "cannot") {
		feasibility = "low"
	}

	// Generate suggestions
	suggestions := []string{
		"Break down complex tasks into smaller steps",
		"Add error handling for each action",
		"Consider adding wait times between actions",
	}

	if complexity == "complex" {
		suggestions = append(suggestions, "Consider using parallel execution for independent tasks")
	}

	if len(actions) > 0 {
		suggestions = append(suggestions, fmt.Sprintf("Primary actions needed: %s", strings.Join(actions, ", ")))
	}

	return &AnalysisResponse{
		Complexity:  complexity,
		Keywords:    keywords,
		Actions:     actions,
		Feasibility: feasibility,
		Suggestions: suggestions,
		Metadata: map[string]any{
			"mock":         true,
			"model":        "mock-gpt",
			"goal_length":  len(goal),
			"action_count": len(actions),
		},
	}, nil
}

// GenerateCode implements code generation with mock logic
func (m *MockLLMClient) GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error) {
	if m.Delay > 0 {
		time.Sleep(m.Delay)
	}

	var code string
	var explanation string

	switch strings.ToLower(language) {
	case "go", "golang":
		code = fmt.Sprintf(`package main

import (
	"fmt"
	"log"
)

// Generated code for: %s
func main() {
	fmt.Println("Mock generated Go code")
	log.Println("This is a mock implementation")
}`, prompt)
		explanation = "Mock Go code generated based on the prompt"

	case "javascript", "js":
		code = fmt.Sprintf(`// Generated code for: %s
function mockFunction() {
    console.log("Mock generated JavaScript code");
    return "This is a mock implementation";
}

mockFunction();`, prompt)
		explanation = "Mock JavaScript code generated based on the prompt"

	case "python":
		code = fmt.Sprintf(`# Generated code for: %s
def mock_function():
    print("Mock generated Python code")
    return "This is a mock implementation"

if __name__ == "__main__":
    mock_function()`, prompt)
		explanation = "Mock Python code generated based on the prompt"

	default:
		code = fmt.Sprintf(`// Generated %s code for: %s
// This is a mock implementation
console.log("Mock generated code");`, language, prompt)
		explanation = fmt.Sprintf("Mock %s code generated based on the prompt", language)
	}

	return &CodeResponse{
		Code:        code,
		Language:    language,
		Explanation: explanation,
		Metadata: map[string]any{
			"mock":          true,
			"model":         "mock-gpt",
			"prompt_length": len(prompt),
		},
	}, nil
}
