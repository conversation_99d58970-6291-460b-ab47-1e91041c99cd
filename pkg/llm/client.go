package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// LLMClient defines the interface for LLM interactions
type LLMClient interface {
	Chat(ctx context.Context, messages []Message) (*Response, error)
	PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error)
	AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error)
	GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error)
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"`    // "system", "user", "assistant"
	Content string `json:"content"`
}

// Response represents LLM response
type Response struct {
	Content   string            `json:"content"`
	Usage     *Usage            `json:"usage,omitempty"`
	Metadata  map[string]any    `json:"metadata,omitempty"`
	Error     string            `json:"error,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// PlanResponse represents task planning response
type PlanResponse struct {
	Tasks       []TaskPlan        `json:"tasks"`
	Description string            `json:"description"`
	Complexity  string            `json:"complexity"`
	Metadata    map[string]any    `json:"metadata,omitempty"`
}

// TaskPlan represents a planned task
type TaskPlan struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	Type         string            `json:"type"`
	Action       string            `json:"action"`
	Parameters   map[string]any    `json:"parameters"`
	Dependencies []string          `json:"dependencies"`
	Priority     int               `json:"priority"`
}

// AnalysisResponse represents goal analysis response
type AnalysisResponse struct {
	Complexity   string            `json:"complexity"`
	Keywords     []string          `json:"keywords"`
	Actions      []string          `json:"actions"`
	Feasibility  string            `json:"feasibility"`
	Suggestions  []string          `json:"suggestions"`
	Metadata     map[string]any    `json:"metadata,omitempty"`
}

// CodeResponse represents code generation response
type CodeResponse struct {
	Code        string            `json:"code"`
	Language    string            `json:"language"`
	Explanation string            `json:"explanation"`
	Metadata    map[string]any    `json:"metadata,omitempty"`
}

// OpenAIClient implements LLMClient for OpenAI API
type OpenAIClient struct {
	APIKey     string
	BaseURL    string
	Model      string
	HTTPClient *http.Client
	Timeout    time.Duration
}

// NewOpenAIClient creates a new OpenAI client
func NewOpenAIClient(apiKey string) *OpenAIClient {
	return &OpenAIClient{
		APIKey:  apiKey,
		BaseURL: "https://api.openai.com/v1",
		Model:   "gpt-3.5-turbo",
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		Timeout: 30 * time.Second,
	}
}

// SetModel sets the model to use
func (c *OpenAIClient) SetModel(model string) *OpenAIClient {
	c.Model = model
	return c
}

// SetTimeout sets the request timeout
func (c *OpenAIClient) SetTimeout(timeout time.Duration) *OpenAIClient {
	c.Timeout = timeout
	c.HTTPClient.Timeout = timeout
	return c
}

// Chat implements LLMClient interface
func (c *OpenAIClient) Chat(ctx context.Context, messages []Message) (*Response, error) {
	requestBody := map[string]any{
		"model":    c.Model,
		"messages": messages,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", c.BaseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return &Response{
			Error: fmt.Sprintf("API error: %d - %s", resp.StatusCode, string(body)),
		}, nil
	}

	var openAIResp struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
		Usage *Usage `json:"usage"`
	}

	if err := json.Unmarshal(body, &openAIResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(openAIResp.Choices) == 0 {
		return &Response{
			Error: "no response choices returned",
		}, nil
	}

	return &Response{
		Content: openAIResp.Choices[0].Message.Content,
		Usage:   openAIResp.Usage,
	}, nil
}

// PlanTasks implements task planning using LLM
func (c *OpenAIClient) PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error) {
	contextStr := ""
	if context != nil {
		contextBytes, _ := json.MarshalIndent(context, "", "  ")
		contextStr = string(contextBytes)
	}

	prompt := fmt.Sprintf(`You are an RPA (Robotic Process Automation) task planner. Given a high-level goal, break it down into specific, executable tasks.

Goal: %s

Context: %s

Available Actions:
- click: Click at coordinates (x, y)
- double_click: Double click at coordinates
- type_text: Type text into input field
- key_press: Press keyboard keys
- wait: Wait for specified duration
- screenshot: Take a screenshot
- scroll: Scroll in a direction
- drag: Drag from one point to another
- move_mouse: Move mouse to coordinates

Please respond with a JSON object containing:
{
  "tasks": [
    {
      "id": "task_1",
      "name": "Task Name",
      "description": "Detailed description",
      "type": "action",
      "action": "click",
      "parameters": {"x": 100, "y": 200},
      "dependencies": [],
      "priority": 1
    }
  ],
  "description": "Overall plan description",
  "complexity": "simple|medium|complex"
}

Make sure tasks are in logical order with proper dependencies.`, goal, contextStr)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA task planner. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get LLM response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("LLM error: %s", response.Error)
	}

	var planResp PlanResponse
	if err := json.Unmarshal([]byte(response.Content), &planResp); err != nil {
		return nil, fmt.Errorf("failed to parse LLM response as JSON: %w", err)
	}

	planResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.Model,
	}

	return &planResp, nil
}

// AnalyzeGoal implements goal analysis using LLM
func (c *OpenAIClient) AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error) {
	prompt := fmt.Sprintf(`Analyze the following RPA goal and provide insights:

Goal: %s

Please respond with a JSON object containing:
{
  "complexity": "simple|medium|complex",
  "keywords": ["keyword1", "keyword2"],
  "actions": ["action1", "action2"],
  "feasibility": "high|medium|low",
  "suggestions": ["suggestion1", "suggestion2"]
}

Consider:
- How many steps are involved
- What RPA actions are needed
- Potential challenges
- Alternative approaches`, goal)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA analyst. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get LLM response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("LLM error: %s", response.Error)
	}

	var analysisResp AnalysisResponse
	if err := json.Unmarshal([]byte(response.Content), &analysisResp); err != nil {
		return nil, fmt.Errorf("failed to parse LLM response as JSON: %w", err)
	}

	analysisResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.Model,
	}

	return &analysisResp, nil
}

// GenerateCode implements code generation using LLM
func (c *OpenAIClient) GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error) {
	fullPrompt := fmt.Sprintf(`Generate %s code for the following requirement:

%s

Please respond with a JSON object containing:
{
  "code": "// Generated code here",
  "language": "%s",
  "explanation": "Explanation of the code"
}`, language, prompt, language)

	messages := []Message{
		{Role: "system", Content: "You are an expert programmer. Always respond with valid JSON containing executable code."},
		{Role: "user", Content: fullPrompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get LLM response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("LLM error: %s", response.Error)
	}

	var codeResp CodeResponse
	if err := json.Unmarshal([]byte(response.Content), &codeResp); err != nil {
		return nil, fmt.Errorf("failed to parse LLM response as JSON: %w", err)
	}

	codeResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.Model,
	}

	return &codeResp, nil
}
