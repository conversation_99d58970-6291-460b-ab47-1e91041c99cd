package llm

import (
	"context"
)

// LLMClient defines the interface for LLM interactions
type LLMClient interface {
	Chat(ctx context.Context, messages []Message) (*Response, error)
	PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error)
	AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error)
	GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error)
}

// Message represents a chat message
type Message struct {
	Role    string `json:"role"` // "system", "user", "assistant"
	Content string `json:"content"`
}

// Response represents LLM response
type Response struct {
	Content  string         `json:"content"`
	Usage    *Usage         `json:"usage,omitempty"`
	Metadata map[string]any `json:"metadata,omitempty"`
	Error    string         `json:"error,omitempty"`
}

// Usage represents token usage information
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// PlanResponse represents task planning response
type PlanResponse struct {
	Tasks       []TaskPlan     `json:"tasks"`
	Description string         `json:"description"`
	Complexity  string         `json:"complexity"`
	Metadata    map[string]any `json:"metadata,omitempty"`
}

// TaskPlan represents a planned task
type TaskPlan struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Type         string         `json:"type"`
	Action       string         `json:"action"`
	Parameters   map[string]any `json:"parameters"`
	Dependencies []string       `json:"dependencies"`
	Priority     int            `json:"priority"`
}

// AnalysisResponse represents goal analysis response
type AnalysisResponse struct {
	Complexity  string         `json:"complexity"`
	Keywords    []string       `json:"keywords"`
	Actions     []string       `json:"actions"`
	Feasibility string         `json:"feasibility"`
	Suggestions []string       `json:"suggestions"`
	Metadata    map[string]any `json:"metadata,omitempty"`
}

// CodeResponse represents code generation response
type CodeResponse struct {
	Code        string         `json:"code"`
	Language    string         `json:"language"`
	Explanation string         `json:"explanation"`
	Metadata    map[string]any `json:"metadata,omitempty"`
}

// Note: OpenAIClient implementation has been moved to openai_client.go
// using the official github.com/openai/openai-go SDK
