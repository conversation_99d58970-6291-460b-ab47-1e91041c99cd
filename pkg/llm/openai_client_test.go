package llm

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOpenAIClientV2(t *testing.T) {
	// Skip if no API key is provided
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		t.Skip("OPENAI_API_KEY not set, skipping OpenAI client tests")
	}

	client := NewOpenAIClientV2(apiKey)
	client.SetModel("gpt-3.5-turbo").SetTimeout(30 * time.Second)

	t.Run("Chat", func(t *testing.T) {
		messages := []Message{
			{Role: "system", Content: "You are a helpful assistant."},
			{Role: "user", Content: "Say hello in a friendly way."},
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.Chat(ctx, messages)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
		assert.Empty(t, response.Error)
		assert.NotNil(t, response.Usage)
		assert.Greater(t, response.Usage.TotalTokens, 0)

		t.Logf("Response: %s", response.Content)
		t.Logf("Usage: %+v", response.Usage)
	})

	t.Run("PlanTasks", func(t *testing.T) {
		goal := "login to a website"

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.PlanTasks(ctx, goal, nil)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Tasks)
		assert.NotEmpty(t, response.Description)
		assert.Contains(t, []string{"simple", "medium", "complex"}, response.Complexity)

		t.Logf("Plan: %s", response.Description)
		t.Logf("Complexity: %s", response.Complexity)
		t.Logf("Tasks: %d", len(response.Tasks))

		for i, task := range response.Tasks {
			t.Logf("Task %d: %s - %s", i+1, task.Name, task.Action)
		}
	})

	t.Run("AnalyzeGoal", func(t *testing.T) {
		goal := "automate data entry into a complex form"

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.AnalyzeGoal(ctx, goal)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Keywords)
		assert.NotEmpty(t, response.Actions)
		assert.Contains(t, []string{"simple", "medium", "complex"}, response.Complexity)
		assert.Contains(t, []string{"high", "medium", "low"}, response.Feasibility)

		t.Logf("Complexity: %s", response.Complexity)
		t.Logf("Feasibility: %s", response.Feasibility)
		t.Logf("Keywords: %v", response.Keywords)
		t.Logf("Actions: %v", response.Actions)
		t.Logf("Suggestions: %v", response.Suggestions)
	})

	t.Run("GenerateCode", func(t *testing.T) {
		prompt := "Create a function that validates an email address"
		language := "go"

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.GenerateCode(ctx, prompt, language)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Code)
		assert.Equal(t, language, response.Language)
		assert.NotEmpty(t, response.Explanation)

		t.Logf("Generated code:\n%s", response.Code)
		t.Logf("Explanation: %s", response.Explanation)
	})
}

func TestOpenAIClientV2WithMockMode(t *testing.T) {
	// Test that factory falls back to mock when no API key
	os.Setenv("LLM_MOCK_MODE", "true")
	defer os.Unsetenv("LLM_MOCK_MODE")

	client := NewClientFromEnv()

	// Should be a mock client
	_, isMock := client.(*MockLLMClient)
	assert.True(t, isMock, "Should fallback to mock client when LLM_MOCK_MODE is true")
}

func TestOpenAIClientV2Configuration(t *testing.T) {
	client := NewOpenAIClientV2("test-key")

	// Test default values
	assert.Equal(t, "gpt-3.5-turbo", client.model)
	assert.Equal(t, 30*time.Second, client.timeout)

	// Test configuration
	client.SetModel("gpt-4").SetTimeout(60 * time.Second)
	assert.Equal(t, "gpt-4", client.model)
	assert.Equal(t, 60*time.Second, client.timeout)
}

func TestExtractCodeFromMarkdown(t *testing.T) {
	testCases := []struct {
		name     string
		content  string
		language string
		expected string
	}{
		{
			name:     "Go code block",
			content:  "Here's some Go code:\n```go\nfunc main() {\n    fmt.Println(\"Hello\")\n}\n```\nThat's it!",
			language: "go",
			expected: "func main() {\n    fmt.Println(\"Hello\")\n}",
		},
		{
			name:     "Generic code block",
			content:  "Here's code:\n```\nfunc test() {}\n```",
			language: "go",
			expected: "func test() {}",
		},
		{
			name:     "No code block",
			content:  "Just some text without code",
			language: "go",
			expected: "",
		},
		{
			name:     "Multiple code blocks",
			content:  "First:\n```go\nfunc a() {}\n```\nSecond:\n```go\nfunc b() {}\n```",
			language: "go",
			expected: "func a() {}",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := extractCodeFromMarkdown(tc.content, tc.language)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestOpenAIClientV2ErrorHandling(t *testing.T) {
	// Test with invalid API key
	client := NewOpenAIClientV2("invalid-key")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	t.Run("Chat with invalid key", func(t *testing.T) {
		messages := []Message{
			{Role: "user", Content: "Hello"},
		}

		response, err := client.Chat(ctx, messages)
		// Should not return error, but response should contain error message
		require.NoError(t, err)
		assert.NotEmpty(t, response.Error)
		assert.Contains(t, response.Error, "OpenAI API error")
	})

	t.Run("PlanTasks with invalid key", func(t *testing.T) {
		_, err := client.PlanTasks(ctx, "test goal", nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "OpenAI error")
	})
}

func TestFactoryWithOpenAIClientV2(t *testing.T) {
	t.Run("With API key", func(t *testing.T) {
		config := &Config{
			Type:   ClientTypeOpenAI,
			APIKey: "test-key",
			Model:  "gpt-4",
		}

		client := NewClient(config)
		assert.NotNil(t, client)

		// Should be OpenAIClientV2
		openaiClient, ok := client.(*OpenAIClientV2)
		assert.True(t, ok, "Should create OpenAIClientV2")
		assert.Equal(t, "gpt-4", openaiClient.model)
	})

	t.Run("Without API key", func(t *testing.T) {
		config := &Config{
			Type:   ClientTypeOpenAI,
			APIKey: "", // No API key
		}

		client := NewClient(config)
		assert.NotNil(t, client)

		// Should fallback to mock
		_, isMock := client.(*MockLLMClient)
		assert.True(t, isMock, "Should fallback to mock when no API key")
	})
}

func BenchmarkOpenAIClientV2(b *testing.B) {
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		b.Skip("OPENAI_API_KEY not set, skipping benchmark")
	}

	client := NewOpenAIClientV2(apiKey)
	messages := []Message{
		{Role: "user", Content: "Say hello"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		_, err := client.Chat(ctx, messages)
		cancel()
		if err != nil {
			b.Fatalf("Chat failed: %v", err)
		}
	}
}
