package llm

import (
	"os"
)

// ClientType represents the type of LLM client
type ClientType string

const (
	ClientTypeOpenAI ClientType = "openai"
	ClientTypeMock   ClientType = "mock"
)

// Config holds LLM client configuration
type Config struct {
	Type     ClientType `json:"type"`
	APIKey   string     `json:"api_key,omitempty"`
	Model    string     `json:"model,omitempty"`
	BaseURL  string     `json:"base_url,omitempty"`
	MockMode bool       `json:"mock_mode,omitempty"`
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		Type:     ClientTypeMock, // Default to mock for development
		Model:    "gpt-3.5-turbo",
		MockMode: true,
	}
}

// NewClient creates a new LLM client based on configuration
func NewClient(config *Config) LLMClient {
	if config == nil {
		config = DefaultConfig()
	}

	// Force mock mode if no API key is provided for OpenAI
	if config.Type == ClientTypeOpenAI && config.APIKey == "" {
		config.Type = ClientTypeMock
		config.MockMode = true
	}

	switch config.Type {
	case ClientTypeOpenAI:
		client := NewOpenAIClient(config.APIKey)
		if config.Model != "" {
			client.SetModel(config.Model)
		}
		if config.BaseURL != "" {
			client.BaseURL = config.BaseURL
		}
		return client

	case ClientTypeMock:
		return NewMockLLMClient()

	default:
		// Fallback to mock
		return NewMockLLMClient()
	}
}

// NewClientFromEnv creates a client using environment variables
func NewClientFromEnv() LLMClient {
	config := &Config{
		Type:   ClientTypeMock, // Default
		Model:  "gpt-3.5-turbo",
	}

	// Check for OpenAI API key
	if apiKey := os.Getenv("OPENAI_API_KEY"); apiKey != "" {
		config.Type = ClientTypeOpenAI
		config.APIKey = apiKey
	}

	// Check for custom model
	if model := os.Getenv("LLM_MODEL"); model != "" {
		config.Model = model
	}

	// Check for custom base URL
	if baseURL := os.Getenv("LLM_BASE_URL"); baseURL != "" {
		config.BaseURL = baseURL
	}

	// Check for mock mode override
	if os.Getenv("LLM_MOCK_MODE") == "true" {
		config.Type = ClientTypeMock
		config.MockMode = true
	}

	return NewClient(config)
}

// GetDefaultClient returns a singleton default client
var defaultClient LLMClient

func GetDefaultClient() LLMClient {
	if defaultClient == nil {
		defaultClient = NewClientFromEnv()
	}
	return defaultClient
}

// SetDefaultClient sets the default client
func SetDefaultClient(client LLMClient) {
	defaultClient = client
}

// Helper functions for common operations

// QuickPlan is a convenience function for quick task planning
func QuickPlan(goal string) (*PlanResponse, error) {
	client := GetDefaultClient()
	return client.PlanTasks(nil, goal, nil)
}

// QuickAnalyze is a convenience function for quick goal analysis
func QuickAnalyze(goal string) (*AnalysisResponse, error) {
	client := GetDefaultClient()
	return client.AnalyzeGoal(nil, goal)
}

// QuickChat is a convenience function for quick chat
func QuickChat(message string) (*Response, error) {
	client := GetDefaultClient()
	messages := []Message{
		{Role: "user", Content: message},
	}
	return client.Chat(nil, messages)
}

// QuickCode is a convenience function for quick code generation
func QuickCode(prompt string, language string) (*CodeResponse, error) {
	client := GetDefaultClient()
	return client.GenerateCode(nil, prompt, language)
}
