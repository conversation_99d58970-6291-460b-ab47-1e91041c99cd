package llm

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMockLLMClient(t *testing.T) {
	client := NewMockLLMClient()

	t.Run("Chat", func(t *testing.T) {
		messages := []Message{
			{Role: "user", Content: "Hello, how are you?"},
		}

		response, err := client.Chat(context.Background(), messages)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
		assert.Contains(t, response.Content, "Mock response")
		assert.NotNil(t, response.Usage)
		assert.True(t, response.Usage.TotalTokens > 0)
	})

	t.Run("PlanTasks", func(t *testing.T) {
		goal := "login to website"

		response, err := client.PlanTasks(context.Background(), goal, nil)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Tasks)
		assert.Equal(t, "Mock plan for: login to website", response.Description)
		assert.NotEmpty(t, response.Complexity)

		// Check that login tasks are generated
		hasLoginTask := false
		hasUsernameTask := false
		hasPasswordTask := false

		for _, task := range response.Tasks {
			if task.Name == "Navigate to login page" {
				hasLoginTask = true
			}
			if task.Name == "Enter username" {
				hasUsernameTask = true
			}
			if task.Name == "Enter password" {
				hasPasswordTask = true
			}
		}

		assert.True(t, hasLoginTask, "Should have login navigation task")
		assert.True(t, hasUsernameTask, "Should have username entry task")
		assert.True(t, hasPasswordTask, "Should have password entry task")
	})

	t.Run("AnalyzeGoal", func(t *testing.T) {
		goal := "click button and type text"

		response, err := client.AnalyzeGoal(context.Background(), goal)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Keywords)
		assert.NotEmpty(t, response.Actions)
		assert.Contains(t, response.Actions, "click")
		assert.Contains(t, response.Actions, "type_text")
		assert.NotEmpty(t, response.Feasibility)
		assert.NotEmpty(t, response.Suggestions)
	})

	t.Run("GenerateCode", func(t *testing.T) {
		prompt := "Create a function that adds two numbers"
		language := "go"

		response, err := client.GenerateCode(context.Background(), prompt, language)
		require.NoError(t, err)
		assert.NotEmpty(t, response.Code)
		assert.Equal(t, language, response.Language)
		assert.NotEmpty(t, response.Explanation)
		assert.Contains(t, response.Code, "package main")
	})
}

func TestLLMFactory(t *testing.T) {
	t.Run("DefaultConfig", func(t *testing.T) {
		config := DefaultConfig()
		assert.Equal(t, ClientTypeMock, config.Type)
		assert.True(t, config.MockMode)
		assert.Equal(t, "gpt-3.5-turbo", config.Model)
	})

	t.Run("NewClient with Mock", func(t *testing.T) {
		config := &Config{
			Type: ClientTypeMock,
		}

		client := NewClient(config)
		assert.NotNil(t, client)

		// Test that it works
		response, err := client.Chat(context.Background(), []Message{
			{Role: "user", Content: "test"},
		})
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
	})

	t.Run("NewClient with OpenAI but no API key", func(t *testing.T) {
		config := &Config{
			Type:   ClientTypeOpenAI,
			APIKey: "", // No API key
		}

		client := NewClient(config)
		assert.NotNil(t, client)

		// Should fallback to mock
		response, err := client.Chat(context.Background(), []Message{
			{Role: "user", Content: "test"},
		})
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
	})

	t.Run("GetDefaultClient", func(t *testing.T) {
		client := GetDefaultClient()
		assert.NotNil(t, client)

		// Test that it works
		response, err := client.Chat(context.Background(), []Message{
			{Role: "user", Content: "test"},
		})
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
	})
}

func TestQuickFunctions(t *testing.T) {
	// Set a mock client as default
	SetDefaultClient(NewMockLLMClient())

	t.Run("QuickPlan", func(t *testing.T) {
		response, err := QuickPlan("click button and wait")
		require.NoError(t, err)
		assert.NotEmpty(t, response.Tasks)
		assert.NotEmpty(t, response.Description)
	})

	t.Run("QuickAnalyze", func(t *testing.T) {
		response, err := QuickAnalyze("complex automation task")
		require.NoError(t, err)
		assert.NotEmpty(t, response.Complexity)
		assert.NotEmpty(t, response.Keywords)
	})

	t.Run("QuickChat", func(t *testing.T) {
		response, err := QuickChat("Hello, world!")
		require.NoError(t, err)
		assert.NotEmpty(t, response.Content)
	})

	t.Run("QuickCode", func(t *testing.T) {
		response, err := QuickCode("create a hello world function", "go")
		require.NoError(t, err)
		assert.NotEmpty(t, response.Code)
		assert.Equal(t, "go", response.Language)
	})
}

func TestMockClientDelay(t *testing.T) {
	client := NewMockLLMClient().SetDelay(50 * time.Millisecond)

	start := time.Now()
	_, err := client.Chat(context.Background(), []Message{
		{Role: "user", Content: "test"},
	})
	duration := time.Since(start)

	require.NoError(t, err)
	assert.GreaterOrEqual(t, duration, 50*time.Millisecond)
}

func TestComplexPlanningScenarios(t *testing.T) {
	client := NewMockLLMClient()

	testCases := []struct {
		name     string
		goal     string
		expected []string // Expected task actions
	}{
		{
			name:     "Simple Click",
			goal:     "click button",
			expected: []string{"click"},
		},
		{
			name:     "Form Filling",
			goal:     "fill form with data",
			expected: []string{"type_text", "click"},
		},
		{
			name:     "Login Process",
			goal:     "login to website",
			expected: []string{"click", "type_text"},
		},
		{
			name:     "Screenshot Task",
			goal:     "take screenshot",
			expected: []string{"screenshot"},
		},
		{
			name:     "Wait Task",
			goal:     "wait for page to load",
			expected: []string{"wait"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			response, err := client.PlanTasks(context.Background(), tc.goal, nil)
			require.NoError(t, err)
			assert.NotEmpty(t, response.Tasks)

			// Check that expected actions are present
			actions := make(map[string]bool)
			for _, task := range response.Tasks {
				actions[task.Action] = true
			}

			for _, expectedAction := range tc.expected {
				assert.True(t, actions[expectedAction],
					"Expected action %s not found in tasks for goal: %s", expectedAction, tc.goal)
			}
		})
	}
}

func TestAnalysisComplexity(t *testing.T) {
	client := NewMockLLMClient()

	testCases := []struct {
		goal               string
		expectedComplexity string
	}{
		{"click", "simple"},
		{"click button and type text in form field", "medium"}, // Updated expectation
		{"login to website, navigate to dashboard, fill complex form with multiple fields, submit and verify", "complex"},
	}

	for _, tc := range testCases {
		t.Run(tc.goal, func(t *testing.T) {
			response, err := client.AnalyzeGoal(context.Background(), tc.goal)
			require.NoError(t, err)
			assert.Equal(t, tc.expectedComplexity, response.Complexity)
		})
	}
}
