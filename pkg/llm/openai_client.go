package llm

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
)

// OpenAIClientV2 implements LLMClient using the official OpenAI Go SDK
type OpenAIClientV2 struct {
	client  *openai.Client
	model   string
	timeout time.Duration
}

// NewOpenAIClientV2 creates a new OpenAI client using the official SDK
func NewOpenAIClientV2(apiKey string) *OpenAIClientV2 {
	client := openai.NewClient(
		option.WithAPIKey(apiKey),
	)

	return &OpenAIClientV2{
		client:  &client,
		model:   "gpt-3.5-turbo",
		timeout: 30 * time.Second,
	}
}

// SetModel sets the model to use
func (c *OpenAIClientV2) SetModel(model string) *OpenAIClientV2 {
	c.model = model
	return c
}

// SetTimeout sets the request timeout
func (c *OpenAIClientV2) SetTimeout(timeout time.Duration) *OpenAIClientV2 {
	c.timeout = timeout
	return c
}

// <PERSON><PERSON> implements LL<PERSON>lient interface using the official SDK
func (c *OpenAIClientV2) Chat(ctx context.Context, messages []Message) (*Response, error) {
	if ctx == nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(context.Background(), c.timeout)
		defer cancel()
	}

	// Convert our messages to OpenAI format
	openaiMessages := make([]openai.ChatCompletionMessageParamUnion, len(messages))
	for i, msg := range messages {
		switch msg.Role {
		case "system":
			openaiMessages[i] = openai.SystemMessage(msg.Content)
		case "user":
			openaiMessages[i] = openai.UserMessage(msg.Content)
		case "assistant":
			openaiMessages[i] = openai.AssistantMessage(msg.Content)
		default:
			openaiMessages[i] = openai.UserMessage(msg.Content)
		}
	}

	// Create chat completion request
	completion, err := c.client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: openaiMessages,
		Model:    openai.ChatModel(c.model),
	})

	if err != nil {
		return &Response{
			Error: fmt.Sprintf("OpenAI API error: %v", err),
		}, nil
	}

	if len(completion.Choices) == 0 {
		return &Response{
			Error: "no response choices returned from OpenAI",
		}, nil
	}

	// Convert usage information
	var usage *Usage
	if completion.Usage.PromptTokens > 0 || completion.Usage.CompletionTokens > 0 {
		usage = &Usage{
			PromptTokens:     int(completion.Usage.PromptTokens),
			CompletionTokens: int(completion.Usage.CompletionTokens),
			TotalTokens:      int(completion.Usage.TotalTokens),
		}
	}

	return &Response{
		Content: completion.Choices[0].Message.Content,
		Usage:   usage,
		Metadata: map[string]any{
			"model":         c.model,
			"finish_reason": completion.Choices[0].FinishReason,
		},
	}, nil
}

// PlanTasks implements task planning using the official OpenAI SDK
func (c *OpenAIClientV2) PlanTasks(ctx context.Context, goal string, context map[string]any) (*PlanResponse, error) {
	contextStr := ""
	if context != nil {
		contextBytes, _ := json.MarshalIndent(context, "", "  ")
		contextStr = string(contextBytes)
	}

	prompt := fmt.Sprintf(`You are an RPA (Robotic Process Automation) task planner. Given a high-level goal, break it down into specific, executable tasks.

Goal: %s

Context: %s

Available Actions:
- click: Click at coordinates (x, y)
- double_click: Double click at coordinates
- type_text: Type text into input field
- key_press: Press keyboard keys
- wait: Wait for specified duration
- screenshot: Take a screenshot
- scroll: Scroll in a direction
- drag: Drag from one point to another
- move_mouse: Move mouse to coordinates

Please respond with a JSON object containing:
{
  "tasks": [
    {
      "id": "task_1",
      "name": "Task Name",
      "description": "Detailed description",
      "type": "action",
      "action": "click",
      "parameters": {"x": 100, "y": 200},
      "dependencies": [],
      "priority": 1
    }
  ],
  "description": "Overall plan description",
  "complexity": "simple|medium|complex"
}

Make sure tasks are in logical order with proper dependencies.`, goal, contextStr)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA task planner. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get OpenAI response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("OpenAI error: %s", response.Error)
	}

	var planResp PlanResponse
	if err := json.Unmarshal([]byte(response.Content), &planResp); err != nil {
		// If JSON parsing fails, try to extract JSON from the response
		jsonContent := extractJSONFromResponse(response.Content)
		if jsonContent != "" {
			if err2 := json.Unmarshal([]byte(jsonContent), &planResp); err2 == nil {
				// Successfully parsed extracted JSON
			} else {
				return nil, fmt.Errorf("failed to parse OpenAI response as JSON: %w (original error: %v)", err2, err)
			}
		} else {
			return nil, fmt.Errorf("failed to parse OpenAI response as JSON: %w", err)
		}
	}

	planResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &planResp, nil
}

// AnalyzeGoal implements goal analysis using the official OpenAI SDK
func (c *OpenAIClientV2) AnalyzeGoal(ctx context.Context, goal string) (*AnalysisResponse, error) {
	prompt := fmt.Sprintf(`Analyze the following RPA goal and provide insights:

Goal: %s

Please respond with a JSON object containing:
{
  "complexity": "simple|medium|complex",
  "keywords": ["keyword1", "keyword2"],
  "actions": ["action1", "action2"],
  "feasibility": "high|medium|low",
  "suggestions": ["suggestion1", "suggestion2"]
}

Consider:
- How many steps are involved
- What RPA actions are needed
- Potential challenges
- Alternative approaches`, goal)

	messages := []Message{
		{Role: "system", Content: "You are an expert RPA analyst. Always respond with valid JSON."},
		{Role: "user", Content: prompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get OpenAI response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("OpenAI error: %s", response.Error)
	}

	var analysisResp AnalysisResponse
	if err := json.Unmarshal([]byte(response.Content), &analysisResp); err != nil {
		return nil, fmt.Errorf("failed to parse OpenAI response as JSON: %w", err)
	}

	analysisResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &analysisResp, nil
}

// GenerateCode implements code generation using the official OpenAI SDK
func (c *OpenAIClientV2) GenerateCode(ctx context.Context, prompt string, language string) (*CodeResponse, error) {
	fullPrompt := fmt.Sprintf(`Generate %s code for the following requirement:

%s

Please respond with a JSON object containing:
{
  "code": "// Generated code here",
  "language": "%s",
  "explanation": "Explanation of the code"
}

Make sure the code is syntactically correct and follows best practices.`, language, prompt, language)

	messages := []Message{
		{Role: "system", Content: "You are an expert programmer. Always respond with valid JSON containing executable code."},
		{Role: "user", Content: fullPrompt},
	}

	response, err := c.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get OpenAI response: %w", err)
	}

	if response.Error != "" {
		return nil, fmt.Errorf("OpenAI error: %s", response.Error)
	}

	var codeResp CodeResponse
	if err := json.Unmarshal([]byte(response.Content), &codeResp); err != nil {
		// If JSON parsing fails, try to extract code from markdown
		code := extractCodeFromMarkdown(response.Content, language)
		if code != "" {
			return &CodeResponse{
				Code:        code,
				Language:    language,
				Explanation: "Code extracted from OpenAI response",
				Metadata: map[string]any{
					"usage":     response.Usage,
					"model":     c.model,
					"extracted": true,
				},
			}, nil
		}
		return nil, fmt.Errorf("failed to parse OpenAI response as JSON: %w", err)
	}

	codeResp.Metadata = map[string]any{
		"usage": response.Usage,
		"model": c.model,
	}

	return &codeResp, nil
}

// extractCodeFromMarkdown extracts code from markdown code blocks
func extractCodeFromMarkdown(content string, language string) string {
	// Look for code blocks with the specified language
	patterns := []string{
		fmt.Sprintf("```%s\n", language),
		fmt.Sprintf("```%s ", language),
		"```\n",
		"``` ",
	}

	for _, pattern := range patterns {
		if idx := strings.Index(content, pattern); idx != -1 {
			start := idx + len(pattern)
			if end := strings.Index(content[start:], "```"); end != -1 {
				return strings.TrimSpace(content[start : start+end])
			}
		}
	}

	return ""
}

// extractJSONFromResponse extracts JSON content from a response that might contain extra text
func extractJSONFromResponse(content string) string {
	// Look for JSON object starting with {
	start := strings.Index(content, "{")
	if start == -1 {
		return ""
	}

	// Find the matching closing brace
	braceCount := 0
	for i := start; i < len(content); i++ {
		if content[i] == '{' {
			braceCount++
		} else if content[i] == '}' {
			braceCount--
			if braceCount == 0 {
				return content[start : i+1]
			}
		}
	}

	return ""
}
